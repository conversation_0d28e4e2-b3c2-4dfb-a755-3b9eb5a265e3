# Doel
Het maken van een simpele webapp waar een gebruiker een account kan registreren, in kan loggen met een gebruikersnaam en wachtwoord, en in zijn account een CSV-bestand kan uploaden.

Dit CSV-bestand moet ook in het account aan te passen zijn, en het aangepaste bestand moet opgeslagen worden in de database. Een geüpload en/of aangepast bestand moet hierna ook weer te downloaden zijn als CSV-bestand.

## Techniek
### Google Cloud Platform
Wij maken gebruik van Google Cloud Platform (GCP) voor de hosting van onze apps. Hier kan je in de Free Tier gratis gebruik van maken om de webapp te hosten.

### Apache of NGINX
Om de app te serveren van de GCP-server kan je gebruik maken van Apache, NGINX, Octane of een andere webserver. Dit kan je zelf instellen op de server.

### Database
Je kan gebruik maken van MySQL (MariaDB) of PostgreSQL voor de app. Denk bij het opbouwen van de database na over hoe een CSV-bestand het beste in de database opgeslagen kan worden, zodat het na het opslaan nog makkelijk bewerkt en opgehaald kan worden.
### PHP
Voor het maken van deze webapp maak je gebruik van Laravel 8. Het is de bedoeling om het in PHP 8.0 te maken. Aan de frontend kant hoeft niet te veel aandacht besteed worden. Een pagina met tekst en input velden volstaat, zonder dat verdere styling nodig is (als het maar overzichtelijk en bruikbaar is).

# Oplevering
De demo van de app moet zoals gezegd op een webserver draaien via GCP, en de sourcecode moet als repo op github beschikbaar zijn.

# CSV-bestand
Bijgevoegd zit een voorbeeld CSV-bestand. Dit bestand moet je dus kunnen uploaden in de webapp. In dit bestand moeten alle velden aangepast kunnen worden.
