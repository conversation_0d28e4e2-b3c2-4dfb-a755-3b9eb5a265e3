# API Documentation - 365 Manage Hours

> **Navigation**: [← Back to Main Documentation](README.md) | [Architecture →](ARCHITECTURE.md) | [Authentication →](AUTHENTICATION.md)

## Overview

The 365 Manage Hours application provides a RESTful API for managing timesheets, user authentication, and CSV data processing. All endpoints follow standard HTTP conventions and return appropriate status codes.

**Related Documentation:**
- [Authentication & Authorization](AUTHENTICATION.md) - Detailed authentication flow
- [Database Schema](DATABASE_SCHEMA.md) - Data structure and relationships
- [Code Examples](CODE_EXAMPLES.md) - Implementation examples

## Authentication

Most endpoints require user authentication via session middleware. Authentication is handled through session cookies.

### Authentication Endpoints

#### POST /login
**Description**: Authenticate user with email and password  
**Middleware**: `GuestMiddleware` (redirects authenticated users)  
**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Validation Rules**:
- `email`: Required, valid email format
- `password`: Required

**Response**: Redirect to `/dashboard` on success, returns login form with errors on failure

#### GET /login
**Description**: Display login form  
**Middleware**: `GuestMiddleware`  
**Response**: Login form view

#### POST /register
**Description**: Register new user account  
**Middleware**: `GuestMiddleware`  
**Request Body**:
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123"
}
```

**Validation Rules**:
- `name`: Required
- `email`: Required, valid email format, unique
- `password`: Required, minimum 8 characters
- `password_confirmation`: Required, must match password

**Response**: Redirect to `/dashboard` on success, returns registration form with errors on failure

#### GET /register
**Description**: Display registration form  
**Middleware**: `GuestMiddleware`  
**Response**: Registration form view

#### POST /logout
**Description**: Logout current user  
**Response**: Redirect to home page with success message

## Dashboard

#### GET /dashboard
**Description**: Display user dashboard with paginated timesheets  
**Middleware**: `AuthMiddleware`  
**Query Parameters**:
- `page` (optional): Page number for pagination (default: 1)

**Response**: Dashboard view with user's timesheets

## Timesheet Management

#### GET /timesheets/{id}/edit
**Description**: Display timesheet edit form  
**Middleware**: `AuthMiddleware`  
**Parameters**:
- `id`: Timesheet ID (integer)

**Response**: Edit form view or redirect with error if timesheet not found

#### POST /timesheets/{id}
**Description**: Update existing timesheet  
**Middleware**: `AuthMiddleware`  
**Parameters**:
- `id`: Timesheet ID (integer)

**Request Body**:
```json
{
  "booking_year": "2024",
  "week_number": "30",
  "date": "2024-07-20",
  "employee_id": "320",
  "hours_worked": "8.5",
  "hours_worked_type": "REGULAR"
}
```

**Validation Rules**:
- `booking_year`: Required, numeric, valid year (1900-2100)
- `week_number`: Required, numeric, range 1-53
- `date`: Required, valid date format (Y-m-d)
- `employee_id`: Required, numeric, positive integer
- `hours_worked`: Required, numeric, non-negative
- `hours_worked_type`: Required, string

**Response**: Redirect to dashboard on success, returns edit form with errors on failure

## CSV Processing

#### POST /process-csv
**Description**: Upload and process CSV timesheet file  
**Middleware**: `AuthMiddleware`  
**Content-Type**: `multipart/form-data`  
**Request Body**:
- `csvFile`: CSV file upload

**File Validation**:
- File type: CSV (text/csv, text/plain, application/csv)
- File size: Maximum 5MB
- Delimiter: Semicolon (;) required
- Headers: Must include Boekjaar, Week, Datum, Persnr, Uren, Uurcode

**Response**: Redirect to dashboard with success/error message

#### GET /export
**Description**: Export user timesheets as CSV file  
**Middleware**: `AuthMiddleware`  
**Response**: CSV file download with filename `timesheets_YYYY-MM-DD.csv`

## Error Handling

All endpoints return appropriate HTTP status codes:
- `200 OK`: Successful GET requests
- `302 Found`: Successful POST requests (redirects)
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation errors

## Request/Response Examples

### Successful Login
```http
POST /login HTTP/1.1
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>&password=password123
```

Response:
```http
HTTP/1.1 302 Found
Location: /dashboard?success=Welcome%20back!
```

### Timesheet Update with Validation Error
```http
POST /timesheets/123 HTTP/1.1
Content-Type: application/x-www-form-urlencoded

booking_year=&week_number=30&date=2024-07-20&employee_id=320&hours_worked=8.5&hours_worked_type=REGULAR
```

Response:
```http
HTTP/1.1 200 OK
Content-Type: text/html

<!-- Edit form with validation errors -->
```

### CSV Export
```http
GET /export HTTP/1.1
Cookie: session_id=abc123...
```

Response:
```http
HTTP/1.1 200 OK
Content-Type: text/csv; charset=utf-8
Content-Disposition: attachment; filename="timesheets_2024-01-15.csv"

Boekjaar;Week;Datum;Persnr;Uren;Uurcode
2024;30;2024-07-20;320;8.5;REGULAR
```
