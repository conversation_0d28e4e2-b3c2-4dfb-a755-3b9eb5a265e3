# Validation Reference - 365 Manage Hours

> **Navigation**: [← Back to Main Documentation](README.md) | [API Documentation →](API_DOCUMENTATION.md) | [Code Examples →](CODE_EXAMPLES.md)

## Overview

This document provides a comprehensive reference for all validation rules, business logic, and data constraints used throughout the 365 Manage Hours application.

**Related Documentation:**
- [API Documentation](API_DOCUMENTATION.md) - Endpoint validation requirements
- [Code Examples](CODE_EXAMPLES.md) - Validation implementation examples
- [Testing Documentation](TESTING.md) - Validation testing patterns

## Request Validation Classes

### LoginRequest

**Purpose**: Validates user login credentials

**Fields**:
- `email`: Required, valid email format
- `password`: Required

**Implementation**:
```php
final class LoginRequest implements Request
{
    public function __construct(
        public string $email,
        public string $password,
    ) {}
}
```

**Validation Rules**:
- Email format validation using `filter_var()`
- Required field validation
- No additional business rules

### RegisterRequest

**Purpose**: Validates user registration data

**Fields**:
- `name`: Required
- `email`: Required, valid email format, unique
- `password`: Required, minimum 8 characters
- `password_confirmation`: Required, must match password

**Tempest Attributes**:
```php
#[Email]
public string $email;

#[Length(min: 8)]
public string $password;
```

**Custom Validation**:
- Password confirmation matching
- Email uniqueness check (repository level)

### TimesheetRequest

**Purpose**: Validates timesheet data entry and updates

**Fields**:
- `booking_year`: Required, numeric, range 1900-2100
- `week_number`: Required, numeric, range 1-53
- `date`: Required, valid date format (Y-m-d)
- `employee_id`: Required, numeric, positive integer
- `hours_worked`: Required, numeric, non-negative
- `hours_worked_type`: Required, string

**Tempest Attributes**:
```php
#[Numeric]
public string $booking_year;

#[Numeric]
public string $week_number;

#[Numeric]
public string $employee_id;

#[Numeric]
public string $hours_worked;
```

**Business Rules**:
```php
public function validateBusinessRules(): array
{
    $errors = [];

    // Year validation
    $year = (int) $this->booking_year;
    if ($year < 1900 || $year > 2100) {
        $errors['booking_year'] = 'Booking year must be between 1900 and 2100';
    }

    // Week validation
    $week = (int) $this->week_number;
    if ($week < 1 || $week > 53) {
        $errors['week_number'] = 'Week number must be between 1 and 53';
    }

    // Date format validation
    if (!empty($this->date)) {
        $dateTime = \DateTime::createFromFormat('Y-m-d', $this->date);
        if (!$dateTime || $dateTime->format('Y-m-d') !== $this->date) {
            $errors['date'] = 'Date must be in YYYY-MM-DD format';
        }
    }

    // Hours validation
    if (isset($this->hours_worked)) {
        $hours = (float) $this->hours_worked;
        if ($hours < 0) {
            $errors['hours_worked'] = 'Hours worked cannot be negative';
        }
    }

    return $errors;
}
```

### CsvUploadRequest

**Purpose**: Validates CSV file uploads

**Fields**:
- `files`: Array containing uploaded file data

**File Validation Rules**:
- **File presence**: Must include 'csvFile' key
- **Upload errors**: Must have UPLOAD_ERR_OK status
- **File type**: Must be text/csv, text/plain, or application/csv
- **File size**: Maximum 5MB
- **Content validation**: Must contain semicolon delimiter
- **Non-empty**: File cannot be empty

**Implementation**:
```php
public function validateCsvFile(): array
{
    $errors = [];

    if (!isset($this->files['csvFile'])) {
        $errors['csvFile'] = 'Please select a CSV file';
        return $errors;
    }

    $file = $this->files['csvFile'];

    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors['csvFile'] = 'Please select a valid CSV file';
        return $errors;
    }

    // File type validation
    $allowedTypes = ['text/csv', 'text/plain', 'application/csv'];
    if (!in_array($file['type'], $allowedTypes)) {
        $errors['csvFile'] = 'Please upload a CSV file';
    }

    // File size validation (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        $errors['csvFile'] = 'File size must be less than 5MB';
    }

    return $errors;
}
```

## Database Constraints

### User Model Constraints

**Table**: `users`

**Constraints**:
- `id`: Primary key, auto-increment
- `name`: NOT NULL, max 255 characters
- `email`: NOT NULL, UNIQUE, max 255 characters, valid email format
- `password`: NOT NULL, hashed with `password_hash()`
- `created_at`: NOT NULL, datetime
- `updated_at`: NOT NULL, datetime

**Model Validation**:
```php
#[Length(min: 1, max: 255)]
public string $name;

#[Email]
#[Length(max: 255)]
public string $email;
```

### Timesheet Model Constraints

**Table**: `timesheets`

**Constraints**:
- `id`: Primary key, auto-increment
- `user_id`: NOT NULL, foreign key to users.id
- `booking_year`: NOT NULL, integer
- `week_number`: NOT NULL, integer
- `date`: NOT NULL, date
- `employee_id`: NOT NULL, integer
- `hours_worked`: NOT NULL, real/float
- `hours_worked_type`: NOT NULL, text
- `created_at`: NOT NULL, datetime
- `updated_at`: NOT NULL, datetime

**Foreign Key Constraints**:
- `user_id` references `users.id` with CASCADE DELETE

## CSV Data Validation

### CSV Structure Requirements

**Delimiter**: Semicolon (;)  
**Headers**: Must include all required columns  
**Encoding**: UTF-8 recommended

**Required Headers** (Dutch format):
- `Boekjaar`: Booking year (integer)
- `Week`: Week number (integer)
- `Datum`: Date (YYYY-MM-DD format)
- `Persnr`: Employee number (integer)
- `Uren`: Hours worked (numeric)
- `Uurcode`: Hour type code (string)

**CSV Validation Class**:
```php
final class Csv
{
    public function validate(): bool
    {
        return !empty($this->Boekjaar) &&
               !empty($this->Week) &&
               !empty($this->Datum) &&
               !empty($this->Persnr) &&
               !empty($this->Uren) &&
               !empty($this->Uurcode);
    }

    public function toModelAttributes(): array
    {
        return [
            'booking_year' => $this->Boekjaar,
            'week_number' => $this->Week,
            'date' => $this->Datum,
            'employee_id' => $this->Persnr,
            'hours_worked' => (float) $this->Uren,
            'hours_worked_type' => $this->Uurcode,
        ];
    }
}
```

## Validation Layers

### 1. Client-Side Validation (HTML5)

Basic HTML5 validation attributes:
- `required` for mandatory fields
- `type="email"` for email validation
- `accept=".csv,.txt"` for file uploads
- `min`/`max` for numeric ranges

### 2. Request Class Validation (Tempest Attributes)

PHP attributes for type validation:
- `#[Email]`: Email format validation
- `#[Numeric]`: Numeric value validation
- `#[Length(min: X, max: Y)]`: String length validation

### 3. Business Rules Validation (Custom Methods)

Custom validation methods in Request classes:
- Date format validation
- Range validation (years, weeks)
- Cross-field validation (password confirmation)
- File content validation

### 4. Database Constraints

Database-level constraints:
- Primary key constraints
- Foreign key constraints
- Unique constraints
- NOT NULL constraints

### 5. Repository-Level Validation

Additional checks in repository methods:
- User ownership verification
- Data existence checks
- Business logic enforcement

## Error Handling Patterns

### Validation Error Response

```php
// Controller validation pattern
$errors = $this->validateTimesheetRequest($timesheetRequest);
if (!empty($errors)) {
    return $this->viewService->withErrors('timesheet/edit', $errors, [
        'timesheet' => $timesheet,
        'old' => $request->body
    ]);
}
```

### Error Message Format

**Field-specific errors**:
```php
$errors = [
    'email' => 'Email is required',
    'password' => 'Password must be at least 8 characters',
    'week_number' => 'Week number must be between 1 and 53'
];
```

**General error messages**:
```php
return $this->viewService->withError('dashboard', 'An error occurred', [
    'timesheets' => []
]);
```

## Testing Validation

### Unit Tests

Test individual validation methods:
```php
public function test_validates_business_rules(): void
{
    $request = new TimesheetRequest(/* invalid data */);
    $errors = $request->validateBusinessRules();
    $this->assertArrayHasKey('week_number', $errors);
}
```

### Feature Tests

Test complete validation flow:
```php
public function test_rejects_invalid_timesheet_data(): void
{
    $response = $this->http->post("/timesheets/{$id}", $invalidData);
    $response->assertOk(); // Returns form with errors
    $this->assertTimesheetUnchanged($id);
}
```

### Data Providers

Test multiple scenarios:
```php
#[DataProvider('invalidDateProvider')]
public function test_rejects_invalid_dates(string $invalidDate): void
{
    // Test implementation
}

public static function invalidDateProvider(): array
{
    return [
        'invalid format' => ['invalid-date'],
        'impossible date' => ['2024-13-45'],
        'letters in date' => ['2024-ab-cd'],
    ];
}
```

This validation reference ensures comprehensive data integrity and user input validation throughout the 365 Manage Hours application.
