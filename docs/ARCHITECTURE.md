# Architecture Overview - 365 Manage Hours

> **Navigation**: [← Back to Main Documentation](README.md) | [API Documentation →](API_DOCUMENTATION.md) | [Database Schema →](DATABASE_SCHEMA.md)

## Application Overview

The 365 Manage Hours application is a timesheet management system built with the Tempest PHP framework. It follows clean architecture principles with clear separation of concerns and modern PHP development practices.

**Related Documentation:**
- [Database Schema](DATABASE_SCHEMA.md) - Detailed database design
- [Code Examples](CODE_EXAMPLES.md) - Implementation patterns
- [Testing Documentation](TESTING.md) - Testing architecture

## Technology Stack

- **Backend**: PHP 8.4+ with Tempest Framework
- **Database**: SQLite with Tempest ORM
- **CSV Processing**: League CSV for robust file handling
- **Testing**: PHPUnit with comprehensive test coverage
- **Frontend**: Server-side rendered views with modern CSS

## Architectural Patterns

### Repository Pattern
Data access is abstracted through repository classes that provide a clean API for database operations:

- **UserRepository**: User management operations (create, find, authenticate)
- **TimesheetRepository**: Timesheet CRUD operations with user-specific queries

```php
// Example repository usage
$user = $this->userRepository->findByEmail($email);
$timesheets = $this->timesheetRepository->getPaginatedForUser($userId, $page);
```

### Service Layer
Business logic is encapsulated in service classes:

- **SessionService**: Session management and authentication state
- **ViewService**: Centralized view rendering with consistent data

```php
// Example service usage
$this->sessionService->login($userId, $email, $name);
return $this->viewService->dashboard(['timesheets' => $timesheets]);
```

### Request Validation
Tempest's native Request classes handle validation with PHP attributes:

- **LoginRequest**: Authentication validation
- **RegisterRequest**: Registration validation with password confirmation
- **TimesheetRequest**: Timesheet validation with business rules
- **CsvUploadRequest**: File upload validation

```php
// Example request validation
$loginRequest = new LoginRequest(
    email: $request->body['email'] ?? '',
    password: $request->body['password'] ?? ''
);
```

### Middleware System
Cross-cutting concerns are handled by middleware:

- **AuthMiddleware**: Protects authenticated routes
- **GuestMiddleware**: Restricts access for authenticated users

## Component Architecture

### Controllers
Controllers follow the single responsibility principle and use dependency injection:

```
app/Controllers/
├── Auth/
│   ├── LoginController.php      # Authentication handling
│   └── RegisterController.php   # User registration
├── BaseController.php           # Shared controller functionality
├── DashboardController.php      # Main dashboard
├── TimesheetController.php      # Timesheet CRUD operations
├── ProcessCsvController.php     # CSV import processing
├── DownloadTimesheetController.php # CSV export
└── DocumentationController.php  # Static documentation
```

### Models
Database models use Tempest's ORM with relationship definitions:

```php
// User model with relationship
final class User
{
    use IsDatabaseModel;
    
    #[HasMany(relationJoin: 'user_id', ownerJoin: 'id')]
    public array $timesheets = [];
}

// Timesheet model with relationship
final class Timesheet
{
    use IsDatabaseModel;
    
    #[BelongsTo(ownerJoin: 'user_id', relationJoin: 'id')]
    public ?User $user = null;
}
```

### Database Layer
Database operations use Tempest's query builder:

```php
// Repository query example
return query(Timesheet::class)
    ->select()
    ->where('user_id = ?', $userId)
    ->orderBy('date DESC')
    ->limit($perPage)
    ->offset($offset)
    ->all();
```

## Request Flow

### Authentication Flow
1. User submits login form
2. `GuestMiddleware` checks if user is already authenticated
3. `LoginController` validates credentials using `LoginRequest`
4. `UserRepository` verifies user credentials
5. `SessionService` creates authenticated session
6. Redirect to dashboard

### Timesheet Management Flow
1. User accesses timesheet edit page
2. `AuthMiddleware` verifies authentication
3. `TimesheetController` loads timesheet using `TimesheetRepository`
4. User submits form with updates
5. `TimesheetRequest` validates input data
6. Business rules validation applied
7. `TimesheetRepository` updates database
8. Redirect with success message

### CSV Processing Flow
1. User uploads CSV file
2. `CsvUploadRequest` validates file format and content
3. `ProcessCsvController` processes CSV using League CSV
4. Data mapped through `Csv` class to model attributes
5. `TimesheetRepository` bulk creates timesheet records
6. Success/error feedback to user

## Security Architecture

### Authentication
- Session-based authentication with secure session management
- Password hashing using PHP's `password_hash()` with default algorithm
- Session timeout (24 hours) with activity tracking
- Middleware-based route protection

### Input Validation
- Multi-layer validation: Request classes + business rules
- SQL injection prevention through parameterized queries
- File upload validation (type, size, content)
- CSRF protection (handled by Tempest framework)

### Data Protection
- User data isolation (users can only access their own timesheets)
- Proper error handling without information disclosure
- Secure file handling for CSV uploads

## Database Design

### Schema Overview
```sql
-- Users table
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    email_verified_at DATETIME,
    password TEXT NOT NULL,
    remember_token TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- Timesheets table
CREATE TABLE timesheets (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    booking_year INTEGER NOT NULL,
    week_number INTEGER NOT NULL,
    date DATE NOT NULL,
    employee_id INTEGER NOT NULL,
    hours_worked REAL NOT NULL,
    hours_worked_type TEXT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### Relationships
- **One-to-Many**: User → Timesheets
- **Foreign Key**: timesheets.user_id → users.id

## Configuration Management

### Environment Configuration
- Database configuration in `app/Config/database.config.php`
- Testing environment uses in-memory SQLite
- Production uses file-based SQLite

### Dependency Management
- Composer for PHP dependencies
- Autoloading with PSR-4 standards
- Development dependencies separated from production

## Deployment Architecture

### Requirements
- PHP 8.4 or higher
- SQLite support
- Composer for dependency management
- Web server (Apache, Nginx, or PHP built-in)

### File Structure
```
365-manage-hours/
├── app/                 # Application code
├── database/           # Database files and migrations
├── public/             # Web root directory
├── tests/              # Test suites
├── vendor/             # Composer dependencies
├── composer.json       # Dependency configuration
└── phpunit.xml        # Testing configuration
```

This architecture provides a solid foundation for maintainable, testable, and scalable timesheet management functionality while following modern PHP development best practices.
