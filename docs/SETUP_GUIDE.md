# Setup & Configuration Guide - 365 Manage Hours

> **Navigation**: [← Back to Main Documentation](README.md) | [Testing →](TESTING.md) | [Code Examples →](CODE_EXAMPLES.md)

**Related Documentation:**
- [Database Schema](DATABASE_SCHEMA.md) - Database configuration details
- [Architecture Overview](ARCHITECTURE.md) - Deployment architecture
- [Testing Documentation](TESTING.md) - Test environment setup

## System Requirements

### Minimum Requirements

- **PHP**: 8.4 or higher
- **Extensions**: 
  - PDO SQLite
  - mbstring
  - fileinfo
  - openssl
- **Composer**: Latest version
- **Web Server**: Apache, Nginx, or PHP built-in server
- **Storage**: 100MB free disk space

### Recommended Environment

- **PHP**: 8.4+ with OPcache enabled
- **Memory**: 256MB PHP memory limit
- **Web Server**: Nginx or Apache with mod_rewrite
- **SSL**: HTTPS enabled for production

## Installation

### 1. Clone Repository

```bash
git clone https://github.com/your-username/365-manage-hours.git
cd 365-manage-hours
```

### 2. Install Dependencies

```bash
# Install PHP dependencies
composer install

# For production, use optimized autoloader
composer install --optimize-autoloader --no-dev
```

### 3. Database Setup

```bash
# Create database directory
mkdir -p database

# Run database migrations
./tempest migrate

# Optional: Seed test data
./tempest db:seed TestDataSeeder
```

### 4. File Permissions

```bash
# Set proper permissions
chmod -R 755 storage/
chmod -R 755 database/
chmod +x tempest
```

### 5. Web Server Configuration

#### Apache Configuration

Create `.htaccess` in the `public` directory:

```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [QSA,L]
</IfModule>

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
```

Virtual host configuration:

```apache
<VirtualHost *:80>
    ServerName 365-manage-hours.local
    DocumentRoot /path/to/365-manage-hours/public
    
    <Directory /path/to/365-manage-hours/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/365-manage-hours_error.log
    CustomLog ${APACHE_LOG_DIR}/365-manage-hours_access.log combined
</VirtualHost>
```

#### Nginx Configuration

```nginx
server {
    listen 80;
    server_name 365-manage-hours.local;
    root /path/to/365-manage-hours/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }
    
    location ~ /(database|tests|vendor) {
        deny all;
    }
}
```

#### PHP Built-in Server (Development Only)

```bash
# Start development server
composer serve
# or
php -S localhost:8000 -t public
```

## Configuration

### Database Configuration

Edit `app/Config/database.config.php`:

```php
<?php

use Tempest\Database\Config\SQLiteConfig;

return new SQLiteConfig(
    path: __DIR__ . '/../../database/database.sqlite',
);
```

For other databases, create appropriate configuration:

```php
// MySQL example
use Tempest\Database\Config\MySQLConfig;

return new MySQLConfig(
    host: 'localhost',
    port: 3306,
    database: '365_manage_hours',
    username: 'your_username',
    password: 'your_password',
);
```

### Environment Configuration

The application uses environment-based configuration. Set environment variables or create configuration files as needed.

#### Development Environment

```bash
# Set environment variables
export APP_ENV=development
export APP_DEBUG=true
export DB_PATH=/path/to/database.sqlite
```

#### Production Environment

```bash
# Set environment variables
export APP_ENV=production
export APP_DEBUG=false
export DB_PATH=/path/to/production/database.sqlite
```

### Session Configuration

Sessions are handled by Tempest framework with default PHP session settings. For production, consider:

```php
// In your web server or PHP configuration
ini_set('session.cookie_secure', 1);     // HTTPS only
ini_set('session.cookie_httponly', 1);   // No JavaScript access
ini_set('session.use_strict_mode', 1);   // Strict session handling
```

## Development Setup

### 1. Development Dependencies

```bash
# Install development dependencies
composer install

# Install with development tools
composer require --dev phpunit/phpunit
```

### 2. Running Tests

```bash
# Run all tests
composer test

# Run specific test suite
./vendor/bin/phpunit --testsuite=Unit
./vendor/bin/phpunit --testsuite=Feature

# Run with coverage
./vendor/bin/phpunit --coverage-html coverage/
```

### 3. Development Server

```bash
# Start development server
composer serve

# Access application
open http://localhost:8000
```

### 4. Database Management

```bash
# Run migrations
./tempest migrate

# Fresh migration (drops all tables)
./tempest migrate:fresh

# Seed test data
./tempest db:seed TestDataSeeder
```

## Production Deployment

### 1. Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.4 and extensions
sudo apt install php8.4 php8.4-fpm php8.4-sqlite3 php8.4-mbstring php8.4-fileinfo

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

### 2. Application Deployment

```bash
# Clone and setup
git clone https://github.com/your-username/365-manage-hours.git
cd 365-manage-hours

# Install production dependencies
composer install --optimize-autoloader --no-dev

# Set up database
./tempest migrate

# Set permissions
sudo chown -R www-data:www-data storage/ database/
sudo chmod -R 755 storage/ database/
```

### 3. Security Configuration

```bash
# Set secure file permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod +x tempest

# Protect sensitive directories
echo "Deny from all" > database/.htaccess
echo "Deny from all" > vendor/.htaccess
```

### 4. SSL/HTTPS Setup

```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Google Cloud Platform Deployment

### 1. App Engine Setup

Create `app.yaml`:

```yaml
runtime: php84

env_variables:
  APP_ENV: production
  APP_DEBUG: false

handlers:
- url: /.*
  script: public/index.php
  
skip_files:
- ^(.*/)?#.*#$
- ^(.*/)?.*~$
- ^(.*/)?.*\.py[co]$
- ^(.*/)?.*/RCS/.*$
- ^(.*/)?\..*$
- ^(.*/)?tests/.*$
- ^(.*/)?vendor/.*/(tests|Tests)/.*$
```

### 2. Deploy to App Engine

```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Initialize project
gcloud init
gcloud app create

# Deploy application
gcloud app deploy

# View application
gcloud app browse
```

## Monitoring & Maintenance

### 1. Log Management

```bash
# View application logs
tail -f log/debug.log

# Rotate logs (add to crontab)
0 0 * * * /usr/bin/find /path/to/logs -name "*.log" -mtime +30 -delete
```

### 2. Database Maintenance

```bash
# Backup database
cp database/database.sqlite database/backup_$(date +%Y%m%d).sqlite

# Vacuum database (reclaim space)
sqlite3 database/database.sqlite "VACUUM;"

# Check database integrity
sqlite3 database/database.sqlite "PRAGMA integrity_check;"
```

### 3. Performance Optimization

```bash
# Enable OPcache in php.ini
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Errors

```bash
# Check database file permissions
ls -la database/database.sqlite

# Verify SQLite extension
php -m | grep sqlite
```

#### 2. File Permission Issues

```bash
# Fix permissions
sudo chown -R www-data:www-data .
sudo chmod -R 755 storage/ database/
```

#### 3. Composer Issues

```bash
# Clear Composer cache
composer clear-cache

# Update dependencies
composer update
```

#### 4. Web Server Issues

```bash
# Check Apache/Nginx error logs
sudo tail -f /var/log/apache2/error.log
sudo tail -f /var/log/nginx/error.log

# Test PHP configuration
php -v
php --ini
```

### Debug Mode

Enable debug mode for development:

```bash
export APP_DEBUG=true
```

This will show detailed error messages and stack traces.

## Support

For additional support:

1. Check the application logs in `log/debug.log`
2. Review the test suite for usage examples
3. Consult the Tempest framework documentation
4. Check GitHub issues for known problems

This setup guide provides everything needed to get the 365 Manage Hours application running in development and production environments.
