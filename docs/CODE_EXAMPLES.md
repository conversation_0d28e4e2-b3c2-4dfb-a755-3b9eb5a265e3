# Code Examples & Usage Patterns - 365 Manage Hours

> **Navigation**: [← Back to Main Documentation](README.md) | [Setup Guide →](SETUP_GUIDE.md) | [API Documentation →](API_DOCUMENTATION.md)

## Overview

This document provides practical code examples and usage patterns for working with the 365 Manage Hours application. These examples demonstrate best practices and common patterns used throughout the codebase.

**Related Documentation:**
- [Architecture Overview](ARCHITECTURE.md) - Design patterns and principles
- [API Documentation](API_DOCUMENTATION.md) - Endpoint implementations
- [Testing Documentation](TESTING.md) - Testing examples

## Controller Patterns

### Basic Controller Structure

```php
<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Services\SessionService;
use App\Services\ViewService;
use App\Repositories\TimesheetRepository;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\View\View;

final readonly class ExampleController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private TimesheetRepository $timesheetRepository,
        private ViewService $viewService
    ) {
        parent::__construct($sessionService);
    }

    #[Get('/example')]
    public function index(): View
    {
        $userId = $this->getAuthenticatedUserId();
        $data = $this->timesheetRepository->getAllForUser($userId);
        
        return $this->viewService->render('example/index', [
            'data' => $data
        ]);
    }
}
```

### Request Handling with Validation

```php
#[Post('/timesheets/{id}', middleware: [AuthMiddleware::class])]
public function update(int $id, Request $request): View|Response
{
    try {
        $userId = $this->getAuthenticatedUserId();
        
        // Find and verify ownership
        $timesheet = $this->timesheetRepository->findByIdForUser($id, $userId);
        if (!$timesheet) {
            return $this->redirectWithError('/dashboard', 'Timesheet not found');
        }

        // Create and validate request
        $timesheetRequest = new TimesheetRequest(
            booking_year: $request->body['booking_year'] ?? '',
            week_number: $request->body['week_number'] ?? '',
            date: $request->body['date'] ?? '',
            employee_id: $request->body['employee_id'] ?? '',
            hours_worked: $request->body['hours_worked'] ?? '',
            hours_worked_type: $request->body['hours_worked_type'] ?? ''
        );

        // Validate input
        $errors = $this->validateTimesheetRequest($timesheetRequest);
        if (!empty($errors)) {
            return $this->viewService->withErrors('timesheet/edit', $errors, [
                'timesheet' => $timesheet,
                'old' => $request->body
            ]);
        }

        // Update timesheet
        $updateData = [
            'booking_year' => (int) $timesheetRequest->booking_year,
            'week_number' => (int) $timesheetRequest->week_number,
            'date' => new \DateTime($timesheetRequest->date),
            'employee_id' => (int) $timesheetRequest->employee_id,
            'hours_worked' => (float) $timesheetRequest->hours_worked,
            'hours_worked_type' => $timesheetRequest->hours_worked_type,
        ];

        $success = $this->timesheetRepository->update($id, $updateData);
        
        if (!$success) {
            return $this->redirectWithError('/dashboard', 'Failed to update timesheet');
        }

        return $this->redirectWithSuccess('/dashboard', 'Timesheet updated successfully');

    } catch (AuthenticationException $e) {
        return $this->redirectWithError('/login', 'Please log in to continue');
    } catch (\Exception $e) {
        return $this->redirectWithError('/dashboard', 'An error occurred while updating the timesheet');
    }
}
```

## Repository Patterns

### Basic Repository Structure

```php
<?php

namespace App\Repositories;

use App\Models\Timesheet;
use DateTime;
use function Tempest\Database\query;

final readonly class TimesheetRepository
{
    /**
     * Find timesheet by ID for specific user (ensures data isolation)
     */
    public function findByIdForUser(int $id, int $userId): ?Timesheet
    {
        return query(Timesheet::class)
            ->select()
            ->where('id = ? AND user_id = ?', $id, $userId)
            ->first();
    }

    /**
     * Get paginated results with metadata
     */
    public function getPaginatedForUser(int $userId, int $page = 1, int $perPage = 25): array
    {
        $offset = ($page - 1) * $perPage;

        $timesheets = query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $userId)
            ->orderBy('date DESC')
            ->limit($perPage)
            ->offset($offset)
            ->all();

        $totalCount = query('timesheets')
            ->count()
            ->where('user_id = ?', $userId)
            ->execute();

        return [
            'timesheets' => $timesheets,
            'totalCount' => $totalCount,
            'totalPages' => ceil($totalCount / $perPage),
            'currentPage' => $page,
            'hasNextPage' => $page < ceil($totalCount / $perPage),
            'hasPrevPage' => $page > 1,
        ];
    }

    /**
     * Create with automatic timestamps
     */
    public function create(array $data): int
    {
        $timesheetData = array_merge($data, [
            'created_at' => new DateTime(),
            'updated_at' => new DateTime(),
        ]);

        return query(Timesheet::class)
            ->insert(...$timesheetData)
            ->execute();
    }

    /**
     * Update with automatic timestamp
     */
    public function update(int $id, array $data): bool
    {
        $updateData = array_merge($data, ['updated_at' => new DateTime()]);
        
        $result = query(Timesheet::class)
            ->update(...$updateData)
            ->where('id = ?', $id)
            ->execute();

        return $result !== false;
    }
}
```

## Request Validation Patterns

### Basic Request Class

```php
<?php

namespace App\Http\Requests;

use Tempest\Http\IsRequest;
use Tempest\Http\Request;
use Tempest\Validation\Rules\Numeric;

final class TimesheetRequest implements Request
{
    use IsRequest;

    public function __construct(
        #[Numeric]
        public string $booking_year,

        #[Numeric]
        public string $week_number,

        public string $date,

        #[Numeric]
        public string $employee_id,

        #[Numeric]
        public string $hours_worked,

        public string $hours_worked_type,
    ) {}

    /**
     * Custom business rules validation
     */
    public function validateBusinessRules(): array
    {
        $errors = [];

        // Check if properties are initialized (validation passed)
        if (!isset($this->booking_year) || !isset($this->week_number)) {
            return $errors; // Skip business rules if basic validation failed
        }

        // Validate booking year range
        $year = (int) $this->booking_year;
        if ($year < 1900 || $year > 2100) {
            $errors['booking_year'] = 'Booking year must be between 1900 and 2100';
        }

        // Validate week number range
        $week = (int) $this->week_number;
        if ($week < 1 || $week > 53) {
            $errors['week_number'] = 'Week number must be between 1 and 53';
        }

        // Validate date format
        if (!empty($this->date)) {
            $dateTime = \DateTime::createFromFormat('Y-m-d', $this->date);
            if (!$dateTime || $dateTime->format('Y-m-d') !== $this->date) {
                $errors['date'] = 'Date must be in YYYY-MM-DD format';
            }
        }

        // Validate hours worked
        if (isset($this->hours_worked)) {
            $hours = (float) $this->hours_worked;
            if ($hours < 0) {
                $errors['hours_worked'] = 'Hours worked cannot be negative';
            }
        }

        return $errors;
    }
}
```

### File Upload Request

```php
<?php

namespace App\Http\Requests;

use Tempest\Http\IsRequest;
use Tempest\Http\Request;

final class CsvUploadRequest implements Request
{
    use IsRequest;

    public function __construct(
        public array $files = [],
    ) {}

    /**
     * Validate CSV file upload
     */
    public function validateCsvFile(): array
    {
        $errors = [];

        if (!isset($this->files['csvFile'])) {
            $errors['csvFile'] = 'Please select a CSV file';
            return $errors;
        }

        $file = $this->files['csvFile'];

        // Check upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors['csvFile'] = 'Please select a valid CSV file';
            return $errors;
        }

        // Validate file type
        $allowedTypes = ['text/csv', 'text/plain', 'application/csv'];
        if (!in_array($file['type'], $allowedTypes)) {
            $errors['csvFile'] = 'Please upload a CSV file';
        }

        // Validate file size (max 5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            $errors['csvFile'] = 'File size must be less than 5MB';
        }

        return $errors;
    }

    /**
     * Validate CSV content structure
     */
    public function validateCsvContent(): array
    {
        $errors = [];

        if (!isset($this->files['csvFile'])) {
            return ['csvFile' => 'Please select a CSV file'];
        }

        $file = $this->files['csvFile'];

        try {
            $content = file_get_contents($file['tmp_name']);

            if ($content === false) {
                $errors['csvFile'] = 'Unable to read CSV file';
                return $errors;
            }

            // Check for required delimiter
            if (!str_contains($content, ';')) {
                $errors['csvFile'] = 'CSV file must use semicolon (;) as delimiter';
            }

            // Check if file is not empty
            if (empty(trim($content))) {
                $errors['csvFile'] = 'CSV file cannot be empty';
            }

        } catch (\Exception $e) {
            $errors['csvFile'] = 'Error reading CSV file: ' . $e->getMessage();
        }

        return $errors;
    }
}
```

## Service Patterns

### Session Service Usage

```php
// Check authentication
if (!$this->sessionService->isAuthenticated()) {
    return $this->redirectWithError('/login', 'Please log in');
}

// Get user data
$user = $this->sessionService->getUser();
$userId = $this->sessionService->getUserId();

// Login user
$this->sessionService->login($userId, $email, $name);
// or with User model
$this->sessionService->loginWithUser($user);

// Logout
$this->sessionService->logout();
```

### View Service Usage

```php
// Basic view rendering
return $this->viewService->render('dashboard', [
    'timesheets' => $timesheets,
    'user' => $user
]);

// Render with errors
return $this->viewService->withErrors('timesheet/edit', $errors, [
    'timesheet' => $timesheet,
    'old' => $oldInput
]);

// Render with single error message
return $this->viewService->withError('dashboard', 'An error occurred', [
    'timesheets' => []
]);
```

## Database Query Patterns

### Basic Queries

```php
// Find single record
$user = query(User::class)
    ->select()
    ->where('email = ?', $email)
    ->first();

// Find multiple records with conditions
$timesheets = query(Timesheet::class)
    ->select()
    ->where('user_id = ? AND booking_year = ?', $userId, $year)
    ->orderBy('date DESC')
    ->all();

// Count records
$count = query('timesheets')
    ->count()
    ->where('user_id = ?', $userId)
    ->execute();
```

### Insert Operations

```php
// Insert single record
$id = query(User::class)
    ->insert(
        name: $name,
        email: $email,
        password: password_hash($password, PASSWORD_DEFAULT),
        created_at: new DateTime(),
        updated_at: new DateTime()
    )
    ->execute();

// Insert with model
$user = new User($name, $email, $password);
$user->save();
```

### Update Operations

```php
// Update with conditions
$result = query(Timesheet::class)
    ->update(
        hours_worked: $newHours,
        updated_at: new DateTime()
    )
    ->where('id = ? AND user_id = ?', $id, $userId)
    ->execute();
```

## CSV Processing Patterns

### CSV Import

```php
use League\Csv\Reader;
use App\Csv;

// Read CSV file
$reader = Reader::createFromPath($file['tmp_name']);
$reader->setDelimiter(';');
$reader->setHeaderOffset(0);

$records = $reader->getRecords();
$timesheetData = [];

foreach ($records as $record) {
    try {
        // Map CSV data to application model
        $csv = new Csv(
            Boekjaar: (int) $record['Boekjaar'],
            Week: (int) $record['Week'],
            Datum: new DateTime($record['Datum']),
            Persnr: (int) $record['Persnr'],
            Uren: $record['Uren'],
            Uurcode: $record['Uurcode']
        );

        if ($csv->validate()) {
            $attributes = $csv->toModelAttributes();
            $attributes['user_id'] = $userId;
            $timesheetData[] = $attributes;
        }
    } catch (Exception $e) {
        // Log error and continue
        continue;
    }
}

// Bulk create records
$processedCount = $this->timesheetRepository->bulkCreate($timesheetData);
```

### CSV Export

```php
use League\Csv\Writer;

// Create CSV content
$csv = Writer::createFromString();

// Add headers
$headers = ['Boekjaar', 'Week', 'Datum', 'Persnr', 'Uren', 'Uurcode'];
$csv->insertOne($headers);

// Add data rows
foreach ($timesheets as $timesheet) {
    $row = [
        $timesheet->booking_year,
        $timesheet->week_number,
        $timesheet->date->format('Y-m-d'),
        $timesheet->employee_id,
        $timesheet->hours_worked,
        $timesheet->hours_worked_type,
    ];
    $csv->insertOne($row);
}

// Return as download response
return new class($csv->toString(), $filename) implements Response {
    public function __construct(
        private string $content,
        private string $filename
    ) {}

    public function send(): void
    {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $this->filename . '"');
        header('Content-Length: ' . strlen($this->content));
        echo $this->content;
    }
};
```

## Testing Patterns

### Feature Test Example

```php
public function test_can_update_timesheet(): void
{
    // Arrange
    [$user, $timesheetId] = $this->createUserAndTimesheet();
    
    $updateData = [
        'booking_year' => '2024',
        'week_number' => '30',
        'date' => '2024-07-20',
        'employee_id' => '320',
        'hours_worked' => '7.5',
        'hours_worked_type' => 'REGULAR',
    ];

    // Act
    $response = $this->http->post("/timesheets/{$timesheetId}", $updateData);

    // Assert
    $response->assertRedirect();
    
    $updatedTimesheet = query(Timesheet::class)
        ->select()
        ->where('id = ?', $timesheetId)
        ->first();
    
    $this->assertEquals(2024, $updatedTimesheet->booking_year);
    $this->assertEquals(30, $updatedTimesheet->week_number);
}

private function createUserAndTimesheet(): array
{
    $user = new User(
        name: 'Test User',
        email: '<EMAIL>',
        password: password_hash('password', PASSWORD_DEFAULT),
    );
    $user->save();

    $timesheetId = query(Timesheet::class)
        ->insert(
            user_id: (int) $user->id,
            booking_year: 2023,
            week_number: 25,
            date: new DateTime('2023-06-15'),
            employee_id: 310,
            hours_worked: 8.0,
            hours_worked_type: 'REGULAR',
            created_at: new DateTime(),
            updated_at: new DateTime(),
        )
        ->execute();

    $sessionService = $this->container->get(SessionService::class);
    $sessionService->login((int) $user->id, $user->email, $user->name);

    return [$user, $timesheetId];
}
```

These patterns provide a solid foundation for extending and maintaining the 365 Manage Hours application while following established conventions and best practices.
