# 365 Manage Hours - Developer Documentation

## Overview

Welcome to the comprehensive developer documentation for the 365 Manage Hours timesheet management application. This documentation provides everything you need to understand, develop, and maintain the application.

## Application Summary

365 Manage Hours is a modern timesheet management system built with the Tempest PHP framework. It allows users to:

- **Register and authenticate** with secure session management
- **Upload CSV timesheet files** with validation and processing
- **Edit individual timesheet records** with comprehensive validation
- **Export timesheet data** as CSV files
- **Manage personal timesheet data** with user isolation

## Technology Stack

- **Backend**: PHP 8.4+ with Tempest Framework
- **Database**: SQLite with Tempest ORM
- **CSV Processing**: League CSV library
- **Testing**: PHPUnit with comprehensive coverage
- **Frontend**: Server-side rendered views

## Documentation Structure

### 📚 Core Documentation

#### [API Documentation](API_DOCUMENTATION.md)
Complete API reference covering all endpoints, request/response formats, validation rules, and authentication requirements.

**Contents:**
- Authentication endpoints (login, register, logout)
- Dashboard and timesheet management
- CSV processing (upload/export)
- Request/response examples
- Error handling patterns

#### [Architecture Overview](ARCHITECTURE.md)
Detailed explanation of the application's architecture, design patterns, and component interactions.

**Contents:**
- Architectural patterns (Repository, Service Layer, Request Validation)
- Component structure and relationships
- Request flow diagrams
- Security architecture
- Database design principles

#### [Database Schema](DATABASE_SCHEMA.md)
Comprehensive database documentation including schema, relationships, and data access patterns.

**Contents:**
- Table definitions and relationships
- Column specifications and constraints
- Migration management
- Query patterns and examples
- Performance considerations

### 🔐 Security & Authentication

#### [Authentication & Authorization](AUTHENTICATION.md)
Complete guide to the authentication system, session management, and security features.

**Contents:**
- Session-based authentication flow
- Password security and hashing
- Middleware protection system
- User authorization and data isolation
- Security best practices

### 🧪 Testing & Quality

#### [Testing Documentation](TESTING.md)
Comprehensive testing guide covering patterns, utilities, and coverage.

**Contents:**
- Test structure and organization
- Testing patterns and best practices
- Database testing with fresh migrations
- Authentication testing patterns
- Validation testing coverage

### 🚀 Setup & Deployment

#### [Setup & Configuration Guide](SETUP_GUIDE.md)
Complete installation and deployment guide for development and production environments.

**Contents:**
- System requirements and dependencies
- Local development setup
- Web server configuration (Apache/Nginx)
- Production deployment strategies
- Google Cloud Platform deployment
- Troubleshooting guide

### 💻 Development

#### [Code Examples & Usage Patterns](CODE_EXAMPLES.md)
Practical code examples and patterns for common development tasks.

**Contents:**
- Controller patterns and best practices
- Repository implementation examples
- Request validation patterns
- Service layer usage
- Database query examples
- CSV processing patterns
- Testing examples

## Quick Start

### For New Developers

1. **Start with [Setup Guide](SETUP_GUIDE.md)** - Get the application running locally
2. **Review [Architecture Overview](ARCHITECTURE.md)** - Understand the system design
3. **Study [Code Examples](CODE_EXAMPLES.md)** - Learn common patterns
4. **Explore [API Documentation](API_DOCUMENTATION.md)** - Understand the endpoints

### For API Integration

1. **Read [API Documentation](API_DOCUMENTATION.md)** - Complete endpoint reference
2. **Check [Authentication Guide](AUTHENTICATION.md)** - Understand auth requirements
3. **Review [Database Schema](DATABASE_SCHEMA.md)** - Understand data structure

### For Testing & QA

1. **Study [Testing Documentation](TESTING.md)** - Understand test patterns
2. **Review [Code Examples](CODE_EXAMPLES.md)** - See testing examples
3. **Check [Setup Guide](SETUP_GUIDE.md)** - Environment configuration

## Key Features

### 🔒 Secure Authentication
- Session-based authentication with 24-hour timeout
- Password hashing with PHP's secure defaults
- Middleware-based route protection
- User data isolation

### 📊 CSV Processing
- Robust file upload validation (type, size, format)
- Semicolon-delimited CSV support
- Bulk data import with error handling
- CSV export functionality

### ✅ Comprehensive Validation
- Multi-layer validation system
- Tempest Request classes with PHP attributes
- Business rule validation
- File upload validation

### 🧪 Extensive Testing
- 95%+ test coverage on critical paths
- Feature tests for all endpoints
- Unit tests for validation logic
- Integration tests with fresh database

### 🏗️ Clean Architecture
- Repository pattern for data access
- Service layer for business logic
- Dependency injection throughout
- Separation of concerns

## Development Principles

### Code Quality
- **Type Safety**: Strict typing with PHP 8.4+ features
- **Dependency Injection**: Constructor injection pattern
- **Single Responsibility**: Each class has one clear purpose
- **Error Handling**: Comprehensive exception handling

### Security First
- **Input Validation**: Multi-layer validation system
- **SQL Injection Prevention**: Parameterized queries
- **Authentication**: Secure session management
- **Authorization**: User-based data isolation

### Testing Culture
- **Test-Driven Development**: Tests guide implementation
- **Comprehensive Coverage**: Unit, feature, and integration tests
- **Fresh Database**: Clean state for each test
- **Real-World Scenarios**: Tests mirror actual usage

## Contributing Guidelines

### Code Standards
- Follow PSR-12 coding standards
- Use strict typing (`declare(strict_types=1)`)
- Write comprehensive tests for new features
- Document public APIs and complex logic

### Testing Requirements
- All new features must include tests
- Maintain 90%+ test coverage
- Test both success and failure scenarios
- Include edge case testing

### Documentation
- Update relevant documentation for changes
- Include code examples for new patterns
- Document breaking changes clearly
- Keep API documentation current

## Support & Resources

### Getting Help
- Review existing documentation first
- Check test files for usage examples
- Examine existing code patterns
- Consult Tempest framework documentation

### Useful Commands

```bash
# Development server
composer serve

# Run tests
composer test

# Database migrations
./tempest migrate

# Generate documentation
./tempest static:generate
```

### File Structure Reference

```
365-manage-hours/
├── app/                    # Application code
│   ├── Controllers/        # HTTP controllers
│   ├── Models/            # Database models
│   ├── Repositories/      # Data access layer
│   ├── Services/          # Business logic
│   ├── Http/Requests/     # Validation classes
│   ├── Middleware/        # HTTP middleware
│   └── Views/             # Template files
├── database/              # Database files and migrations
├── docs/                  # This documentation
├── tests/                 # Test suites
└── public/                # Web root
```

This documentation is designed to be your complete guide to working with the 365 Manage Hours application. Each section builds upon the others to provide a comprehensive understanding of the system.

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Framework**: Tempest PHP Framework
