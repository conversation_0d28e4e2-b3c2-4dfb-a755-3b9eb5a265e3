# Database Schema Documentation - 365 Manage Hours

> **Navigation**: [← Back to Main Documentation](README.md) | [Architecture →](ARCHITECTURE.md) | [Authentication →](AUTHENTICATION.md)

## Overview

The 365 Manage Hours application uses SQLite as its database engine with Tempest ORM for data access. The schema is designed to support user authentication and timesheet management with proper relationships and constraints.

**Related Documentation:**
- [Architecture Overview](ARCHITECTURE.md) - Database layer architecture
- [Code Examples](CODE_EXAMPLES.md) - Query patterns and examples
- [Setup Guide](SETUP_GUIDE.md) - Database configuration

## Database Configuration

**Engine**: SQLite  
**Location**: `database/database.sqlite`  
**Testing**: In-memory SQLite (`:memory:`)  
**ORM**: Tempest Database with query builder

## Schema Overview

The database consists of two main tables with a one-to-many relationship:

```
users (1) ──── (many) timesheets
```

## Table Definitions

### users

Stores user account information for authentication and identification.

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    email_verified_at DATETIME NULL,
    password TEXT NOT NULL,
    remember_token TEXT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- Indexes
CREATE UNIQUE INDEX idx_users_email ON users(email);
```

#### Column Details

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | INTEGER | PRIMARY KEY, AUTO INCREMENT | Unique user identifier |
| `name` | TEXT | NOT NULL | User's display name |
| `email` | TEXT | NOT NULL, UNIQUE | User's email address (login identifier) |
| `email_verified_at` | DATETIME | NULLABLE | Email verification timestamp |
| `password` | TEXT | NOT NULL | Hashed password using PHP's password_hash() |
| `remember_token` | TEXT | NULLABLE | Token for "remember me" functionality |
| `created_at` | DATETIME | NOT NULL | Account creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last modification timestamp |

#### Validation Rules (Model Level)

```php
#[Length(min: 1, max: 255)]
public string $name;

#[Email]
#[Length(max: 255)]
public string $email;
```

### timesheets

Stores timesheet entries with work hours and related metadata.

```sql
CREATE TABLE timesheets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    booking_year INTEGER NOT NULL,
    week_number INTEGER NOT NULL,
    date DATE NOT NULL,
    employee_id INTEGER NOT NULL,
    hours_worked REAL NOT NULL,
    hours_worked_type TEXT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX idx_timesheets_user_id ON timesheets(user_id);
CREATE INDEX idx_timesheets_date ON timesheets(date);
CREATE INDEX idx_timesheets_user_date ON timesheets(user_id, date);
```

#### Column Details

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | INTEGER | PRIMARY KEY, AUTO INCREMENT | Unique timesheet entry identifier |
| `user_id` | INTEGER | NOT NULL, FOREIGN KEY | Reference to users.id |
| `booking_year` | INTEGER | NOT NULL | Year for timesheet entry |
| `week_number` | INTEGER | NOT NULL | Week number (1-53) |
| `date` | DATE | NOT NULL | Specific date of work |
| `employee_id` | INTEGER | NOT NULL | Employee identifier |
| `hours_worked` | REAL | NOT NULL | Number of hours worked |
| `hours_worked_type` | TEXT | NOT NULL | Type/category of hours worked |
| `created_at` | DATETIME | NOT NULL | Record creation timestamp |
| `updated_at` | DATETIME | NOT NULL | Last modification timestamp |

#### Business Rules (Application Level)

- `booking_year`: Must be between 1900 and 2100
- `week_number`: Must be between 1 and 53
- `date`: Must be valid date format (Y-m-d)
- `employee_id`: Must be positive integer
- `hours_worked`: Must be non-negative number
- `hours_worked_type`: Required string field

## Relationships

### User → Timesheets (One-to-Many)

```php
// User model
#[HasMany(relationJoin: 'user_id', ownerJoin: 'id')]
public array $timesheets = [];

// Timesheet model
#[BelongsTo(ownerJoin: 'user_id', relationJoin: 'id')]
public ?User $user = null;
```

**Relationship Rules**:
- Each user can have multiple timesheet entries
- Each timesheet belongs to exactly one user
- Cascade delete: Deleting a user removes all their timesheets
- Users can only access their own timesheet data

## Migrations

### Migration Files

1. **2025-01-01_create_users_table.php**
   - Creates users table with all columns and constraints
   - Adds unique constraint on email field

2. **2025-01-02_create_timesheets_table.php**
   - Creates timesheets table with foreign key to users
   - Establishes relationship constraints

### Migration Commands

```bash
# Run migrations
./tempest migrate

# Drop all tables and re-run migrations
./tempest migrate:fresh
```

## Data Access Patterns

### Repository Pattern

Data access is abstracted through repository classes:

#### UserRepository

```php
// Find user by email
$user = $userRepository->findByEmail('<EMAIL>');

// Create new user
$userId = $userRepository->create($name, $email, $password);

// Verify password
$isValid = $userRepository->verifyPassword($user, $password);
```

#### TimesheetRepository

```php
// Get paginated timesheets for user
$result = $timesheetRepository->getPaginatedForUser($userId, $page, $perPage);

// Find specific timesheet for user
$timesheet = $timesheetRepository->findByIdForUser($id, $userId);

// Bulk create from CSV
$count = $timesheetRepository->bulkCreate($timesheetData);
```

### Query Examples

#### User Queries

```php
// Find user by email
$user = query(User::class)
    ->select()
    ->where('email = ?', $email)
    ->first();

// Create user
$userId = query(User::class)
    ->insert(
        name: $name,
        email: $email,
        password: password_hash($password, PASSWORD_DEFAULT),
        created_at: new DateTime(),
        updated_at: new DateTime()
    )
    ->execute();
```

#### Timesheet Queries

```php
// Get user's timesheets with pagination
$timesheets = query(Timesheet::class)
    ->select()
    ->where('user_id = ?', $userId)
    ->orderBy('date DESC')
    ->limit($perPage)
    ->offset($offset)
    ->all();

// Update timesheet
query(Timesheet::class)
    ->update(
        booking_year: $year,
        week_number: $week,
        hours_worked: $hours,
        updated_at: new DateTime()
    )
    ->where('id = ? AND user_id = ?', $id, $userId)
    ->execute();
```

## Data Integrity

### Constraints

1. **Primary Keys**: Auto-incrementing integers for all tables
2. **Foreign Keys**: Proper referential integrity with cascade delete
3. **Unique Constraints**: Email addresses must be unique
4. **Not Null**: Required fields enforced at database level

### Validation Layers

1. **Database Level**: Column constraints and foreign keys
2. **Model Level**: Tempest validation attributes
3. **Request Level**: Form validation in Request classes
4. **Business Logic**: Custom validation rules in controllers

## Performance Considerations

### Indexing Strategy

- **Primary Keys**: Automatic indexing on id columns
- **Foreign Keys**: Index on user_id for efficient joins
- **Query Optimization**: Composite index on (user_id, date) for common queries
- **Email Lookup**: Unique index on email for authentication

### Query Optimization

- Use repository pattern to centralize and optimize queries
- Implement pagination for large datasets
- Use specific column selection when full models aren't needed
- Leverage foreign key relationships for efficient joins

## Backup and Maintenance

### SQLite Maintenance

```bash
# Backup database
cp database/database.sqlite database/backup_$(date +%Y%m%d).sqlite

# Vacuum database (reclaim space)
sqlite3 database/database.sqlite "VACUUM;"

# Check integrity
sqlite3 database/database.sqlite "PRAGMA integrity_check;"
```

### Testing Database

- Tests use in-memory SQLite for speed
- Fresh migrations run for each test
- Test data seeding available via `TestDataSeeder`

This schema design provides a solid foundation for the timesheet management system while maintaining data integrity, performance, and scalability.
