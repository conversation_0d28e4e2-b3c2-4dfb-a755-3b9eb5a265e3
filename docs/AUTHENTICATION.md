# Authentication & Authorization - 365 Manage Hours

> **Navigation**: [← Back to Main Documentation](README.md) | [API Documentation →](API_DOCUMENTATION.md) | [Testing →](TESTING.md)

## Overview

The 365 Manage Hours application implements a session-based authentication system with role-based access control. Authentication is handled through the `SessionService` and protected by middleware components.

**Related Documentation:**
- [API Documentation](API_DOCUMENTATION.md) - Authentication endpoints
- [Architecture Overview](ARCHITECTURE.md) - Security architecture
- [Code Examples](CODE_EXAMPLES.md) - Authentication patterns

## Authentication System

### Session-Based Authentication

The application uses PHP sessions managed through <PERSON><PERSON>'s session handling with the following characteristics:

- **Session Storage**: Server-side session storage
- **Session Timeout**: 24 hours of inactivity
- **Activity Tracking**: Last activity timestamp updated on each request
- **Secure Logout**: Complete session destruction on logout

### Password Security

- **Hashing Algorithm**: <PERSON><PERSON>'s `password_hash()` with `PASSWORD_DEFAULT`
- **Verification**: `password_verify()` for credential checking
- **Minimum Length**: 8 characters required
- **No Password Reset**: Currently not implemented (future enhancement)

## Authentication Flow

### Registration Process

1. User submits registration form
2. `GuestMiddleware` ensures user is not already authenticated
3. `RegisterRequest` validates input data:
   - Name: Required
   - Email: Required, valid format, unique
   - Password: Required, minimum 8 characters
   - Password Confirmation: Must match password
4. `UserRepository` creates new user with hashed password
5. `SessionService` automatically logs in the new user
6. Redirect to dashboard

```php
// Registration validation example
$registerRequest = new RegisterRequest(
    name: $request->body['name'] ?? '',
    email: $request->body['email'] ?? '',
    password: $request->body['password'] ?? '',
    password_confirmation: $request->body['password_confirmation'] ?? ''
);
```

### Login Process

1. User submits login credentials
2. `GuestMiddleware` checks if user is already authenticated
3. `LoginRequest` validates input:
   - Email: Required, valid format
   - Password: Required
4. `UserRepository` finds user by email
5. Password verification using `password_verify()`
6. `SessionService` creates authenticated session
7. Redirect to dashboard

```php
// Login authentication example
$user = $this->userRepository->findByEmail($loginRequest->email);
if (!$user || !$this->userRepository->verifyPassword($user, $loginRequest->password)) {
    throw new AuthenticationException('Invalid credentials');
}
$this->sessionService->loginWithUser($user);
```

### Logout Process

1. User initiates logout (POST request)
2. `SessionService` destroys session data
3. Redirect to home page with success message

## Session Management

### SessionService

The `SessionService` provides centralized session management:

```php
final readonly class SessionService
{
    // Session keys
    private const string USER_ID_KEY = 'user_id';
    private const string USER_EMAIL_KEY = 'user_email';
    private const string USER_NAME_KEY = 'user_name';
    private const string LAST_ACTIVITY_KEY = 'last_activity';
}
```

#### Key Methods

```php
// Check authentication status
public function isAuthenticated(): bool

// Get authenticated user ID
public function getUserId(): ?int

// Get user data array
public function getUser(): ?array

// Get full user model
public function getUserModel(): ?User

// Login user
public function login(int $userId, string $email, string $name = ''): void
public function loginWithUser(User $user): void

// Logout user
public function logout(): void
```

### Session Security Features

1. **Activity Tracking**: Updates last activity on each authenticated request
2. **Session Timeout**: Automatically expires sessions after 24 hours
3. **Session Destruction**: Complete cleanup on logout
4. **Type Safety**: Proper type casting for session data

## Authorization & Middleware

### Middleware Components

#### AuthMiddleware

Protects routes that require authentication:

```php
#[Get('/dashboard', middleware: [AuthMiddleware::class])]
public function dashboard(): View
```

**Behavior**:
- Checks `SessionService::isAuthenticated()`
- Redirects to login page if not authenticated
- Allows request to continue if authenticated

#### GuestMiddleware

Restricts access for already authenticated users:

```php
#[Get('/login', middleware: [GuestMiddleware::class])]
public function show(): View
```

**Behavior**:
- Checks `SessionService::isAuthenticated()`
- Redirects to dashboard if already authenticated
- Allows request to continue if not authenticated

### Route Protection

#### Public Routes
- `GET /` - Home page
- `GET /login` - Login form (guest only)
- `POST /login` - Login processing (guest only)
- `GET /register` - Registration form (guest only)
- `POST /register` - Registration processing (guest only)

#### Protected Routes (Require Authentication)
- `GET /dashboard` - User dashboard
- `GET /timesheets/{id}/edit` - Edit timesheet
- `POST /timesheets/{id}` - Update timesheet
- `POST /process-csv` - CSV upload
- `GET /export` - CSV export
- `POST /logout` - Logout

## User Authorization

### Data Isolation

The application implements user-based data isolation:

1. **Timesheet Access**: Users can only access their own timesheets
2. **Repository Filtering**: All timesheet queries include user_id filtering
3. **Controller Validation**: Controllers verify ownership before operations

```php
// Example: Timesheet access control
public function findByIdForUser(int $id, int $userId): ?Timesheet
{
    return query(Timesheet::class)
        ->select()
        ->where('id = ? AND user_id = ?', $id, $userId)
        ->first();
}
```

### Permission Checks

```php
// Base controller helper for authentication
protected function getAuthenticatedUserId(): int
{
    $userId = $this->sessionService->getUserId();
    if ($userId === null) {
        throw new AuthenticationException('User not authenticated');
    }
    return $userId;
}
```

## Security Considerations

### Input Validation

1. **Email Validation**: Proper email format checking
2. **Password Strength**: Minimum length requirements
3. **CSRF Protection**: Handled by Tempest framework
4. **SQL Injection Prevention**: Parameterized queries

### Session Security

1. **Session Fixation**: New session on login
2. **Session Hijacking**: Activity-based timeout
3. **Secure Cookies**: Framework-managed cookie security
4. **Session Storage**: Server-side storage only

### Error Handling

1. **Information Disclosure**: Generic error messages for authentication failures
2. **Timing Attacks**: Consistent response times for login attempts
3. **Exception Handling**: Proper exception catching and user-friendly messages

## Implementation Examples

### Controller Authentication

```php
abstract readonly class BaseController
{
    public function __construct(protected SessionService $sessionService) {}

    protected function getAuthenticatedUserId(): int
    {
        $userId = $this->sessionService->getUserId();
        if ($userId === null) {
            throw new AuthenticationException('User not authenticated');
        }
        return $userId;
    }
}
```

### Middleware Usage

```php
#[Post('/timesheets/{id}', middleware: [AuthMiddleware::class])]
public function update(int $id, Request $request): View|Response
{
    $userId = $this->getAuthenticatedUserId();
    $timesheet = $this->timesheetRepository->findByIdForUser($id, $userId);
    
    if (!$timesheet) {
        return $this->redirectWithError('/dashboard', 'Timesheet not found');
    }
    
    // Process update...
}
```

### Session Management in Tests

```php
// Test authentication setup
$sessionService = $this->container->get(SessionService::class);
$sessionService->login((int) $user->id, $user->email, $user->name);

// Verify authentication
$this->assertTrue($sessionService->isAuthenticated());
```

## Future Enhancements

### Potential Security Improvements

1. **Password Reset**: Email-based password reset functionality
2. **Two-Factor Authentication**: TOTP or SMS-based 2FA
3. **Account Lockout**: Temporary lockout after failed attempts
4. **Password History**: Prevent password reuse
5. **Session Management**: Multiple device session tracking
6. **Audit Logging**: Track authentication events

### Role-Based Access Control

Currently, the application has a simple user model. Future enhancements could include:

1. **User Roles**: Admin, Manager, Employee roles
2. **Permissions**: Granular permission system
3. **Team Management**: Multi-tenant organization support
4. **Resource-Based Permissions**: Fine-grained access control

This authentication system provides a solid foundation for secure user management while maintaining simplicity and ease of use.
