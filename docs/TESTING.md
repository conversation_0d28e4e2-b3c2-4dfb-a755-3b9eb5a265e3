# Testing Documentation - 365 Manage Hours

> **Navigation**: [← Back to Main Documentation](README.md) | [Authentication →](AUTHENTICATION.md) | [Setup Guide →](SETUP_GUIDE.md)

## Overview

The 365 Manage Hours application has a comprehensive test suite built with PHPUnit, following Tempest framework testing patterns. The test suite covers unit tests, feature tests, and integration tests with high coverage of critical functionality.

**Related Documentation:**
- [Code Examples](CODE_EXAMPLES.md) - Testing patterns and examples
- [Setup Guide](SETUP_GUIDE.md) - Test environment setup
- [Architecture Overview](ARCHITECTURE.md) - Testing architecture

## Test Structure

### Test Organization

```
tests/
├── Feature/                    # Feature/Integration tests
│   ├── Auth/                  # Authentication tests
│   │   ├── AuthenticationTest.php
│   │   └── EmailVerificationTest.php
│   ├── Http/                  # HTTP endpoint tests
│   │   ├── Controllers/       # Controller tests
│   │   └── Requests/          # Request validation tests
│   ├── ExampleTest.php        # Basic application tests
│   └── ProfileTest.php        # User profile tests
├── Unit/                      # Unit tests
│   ├── Http/                  # HTTP component tests
│   │   └── Requests/          # Request class tests
│   └── ExampleTest.php        # Basic unit tests
├── Fixtures/                  # Test fixtures and data
└── IntegrationTestCase.php    # Base test case
```

### Test Configuration

**PHPUnit Configuration** (`phpunit.xml`):
```xml
<phpunit bootstrap="vendor/autoload.php" colors="true">
    <testsuites>
        <testsuite name="Unit">
            <directory>tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory>tests/Feature</directory>
        </testsuite>
    </testsuites>
    <php>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_DEBUG" value="true"/>
        <env name="DB_PATH" value=":memory:"/>
    </php>
</phpunit>
```

## Testing Patterns

### Base Test Case

All integration tests extend `IntegrationTestCase`:

```php
abstract class IntegrationTestCase extends IntegrationTest
{
    protected string $root = __DIR__ . '/../';

    protected function setUp(): void
    {
        parent::setUp();
        
        // Force testing environment
        $appConfig = $this->container->get(\Tempest\Core\AppConfig::class);
        $reflection = new \ReflectionProperty($appConfig, 'environment');
        $reflection->setAccessible(true);
        $reflection->setValue($appConfig, \Tempest\Core\Environment::TESTING);

        // Fresh database for each test
        $migrationManager = $this->container->get(MigrationManager::class);
        $migrationManager->dropAll();
        $migrationManager->up();
    }
}
```

### Database Testing

#### Fresh Database Per Test
Each test gets a clean database state:
- Uses in-memory SQLite (`:memory:`)
- Runs fresh migrations for each test
- No data persistence between tests

#### Test Data Creation
```php
// Create test user
$user = new User(
    name: 'Test User',
    email: '<EMAIL>',
    password: password_hash('password', PASSWORD_DEFAULT),
);
$user->save();

// Create test timesheet
$timesheetId = query(Timesheet::class)
    ->insert(
        user_id: (int) $user->id,
        booking_year: 2023,
        week_number: 25,
        date: new DateTime('2023-06-15'),
        employee_id: 310,
        hours_worked: 8.0,
        hours_worked_type: 'REGULAR',
        created_at: new DateTime(),
        updated_at: new DateTime(),
    )
    ->execute();
```

### Authentication Testing

#### Session Management in Tests
```php
// Authenticate user for testing
$sessionService = $this->container->get(SessionService::class);
$sessionService->login((int) $user->id, $user->email, $user->name);

// Verify authentication
$this->assertTrue($sessionService->isAuthenticated());
```

#### Testing Protected Routes
```php
public function test_dashboard_requires_authentication(): void
{
    // Test without authentication
    $response = $this->http->get('/dashboard');
    $response->assertRedirect('/login');
    
    // Test with authentication
    $this->authenticateUser();
    $response = $this->http->get('/dashboard');
    $response->assertOk();
}
```

## Test Categories

### Unit Tests

#### Request Validation Tests
```php
class TimesheetRequestTest extends TestCase
{
    #[Test]
    public function validateBusinessRules_returns_empty_array_for_valid_data(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();
        $this->assertEmpty($errors);
    }
}
```

#### Data Provider Testing
```php
#[Test]
#[DataProvider('invalidDateProvider')]
public function validateBusinessRules_rejects_invalid_dates(string $invalidDate): void
{
    $request = new TimesheetRequest(/* ... */);
    $errors = $request->validateBusinessRules();
    $this->assertArrayHasKey('date', $errors);
}

public static function invalidDateProvider(): array
{
    return [
        'invalid format' => ['invalid-date'],
        'impossible date' => ['2024-13-45'],
        'letters in date' => ['2024-ab-cd'],
    ];
}
```

### Feature Tests

#### HTTP Endpoint Testing
```php
public function test_can_update_a_timesheet(): void
{
    [$user, $timesheetId] = $this->createUserAndTimesheet();
    
    $updateData = [
        'booking_year' => '2024',
        'week_number' => '30',
        'date' => '2024-07-20',
        'employee_id' => '320',
        'hours_worked' => '7.5',
        'hours_worked_type' => 'TZFG100',
    ];

    $response = $this->http->post("/timesheets/{$timesheetId}", $updateData);
    $response->assertRedirect();
    
    // Verify database update
    $updatedTimesheet = query(Timesheet::class)
        ->select()
        ->where('id = ?', $timesheetId)
        ->first();
    
    $this->assertEquals(2024, $updatedTimesheet->booking_year);
    $this->assertEquals(30, $updatedTimesheet->week_number);
}
```

#### Authentication Flow Testing
```php
public function test_users_can_authenticate_using_the_login_screen(): void
{
    $user = new User(
        name: 'Test User',
        email: '<EMAIL>',
        password: password_hash('password', PASSWORD_DEFAULT),
    );
    $user->save();

    $response = $this->http->post('/login', [
        'email' => $user->email,
        'password' => 'password',
    ]);

    $response->assertRedirect();
    
    $sessionService = $this->container->get(SessionService::class);
    $this->assertTrue($sessionService->isAuthenticated());
}
```

### Validation Testing

#### Comprehensive Validation Coverage
The test suite includes extensive validation testing:

1. **Required Field Validation**
2. **Data Type Validation** (numeric, email, etc.)
3. **Business Rule Validation** (date ranges, week numbers)
4. **Boundary Value Testing**
5. **File Upload Validation**

```php
public function test_validates_week_number_boundaries(): void
{
    [$user, $timesheetId] = $this->createUserAndTimesheet();

    // Test invalid week numbers
    foreach (['0', '54', '-1'] as $invalidWeek) {
        $invalidData = [
            'week_number' => $invalidWeek,
            // ... other fields
        ];
        
        $response = $this->http->post("/timesheets/{$timesheetId}", $invalidData);
        $response->assertOk(); // Should return form with errors
        $this->assertTimesheetUnchanged($timesheetId);
    }
}
```

## Test Utilities

### Helper Methods

```php
// Create user and timesheet for testing
private function createUserAndTimesheet(): array
{
    $user = new User(
        name: 'Test User',
        email: '<EMAIL>',
        password: password_hash('password', PASSWORD_DEFAULT),
    );
    $user->save();

    $timesheetId = query(Timesheet::class)->insert(/* ... */)->execute();
    
    $sessionService = $this->container->get(SessionService::class);
    $sessionService->login((int) $user->id, $user->email, $user->name);

    return [$user, $timesheetId];
}

// Verify timesheet wasn't modified
private function assertTimesheetUnchanged(int $timesheetId): void
{
    $timesheet = query(Timesheet::class)
        ->select()
        ->where('id = ?', $timesheetId)
        ->first();
    
    $this->assertEquals(2023, $timesheet->booking_year);
    $this->assertEquals(25, $timesheet->week_number);
}
```

### Test Data Seeding

```php
final class TestDataSeeder implements DatabaseSeeder
{
    public function run(null|string|UnitEnum $database): void
    {
        // Create test user
        $userId = query(User::class)
            ->insert(
                name: 'Test User',
                email: '<EMAIL>',
                password: password_hash('password', PASSWORD_DEFAULT),
                created_at: new DateTime(),
                updated_at: new DateTime(),
            )
            ->onDatabase($database)
            ->execute();

        // Create test timesheets
        $timesheets = [/* ... test data ... */];
        foreach ($timesheets as $timesheet) {
            query(Timesheet::class)
                ->insert(...$timesheet)
                ->onDatabase($database)
                ->execute();
        }
    }
}
```

## Running Tests

### Command Line

```bash
# Run all tests
composer test
# or
phpunit

# Run specific test suite
phpunit --testsuite=Unit
phpunit --testsuite=Feature

# Run specific test file
phpunit tests/Feature/Auth/AuthenticationTest.php

# Run with coverage
phpunit --coverage-html coverage/
```

### Test Environment

Tests automatically use:
- **Environment**: `testing`
- **Database**: In-memory SQLite
- **Debug Mode**: Enabled
- **Fresh Migrations**: For each test

## Test Coverage

### Current Coverage Areas

1. **Authentication System** (95%+)
   - Login/logout flows
   - Registration process
   - Session management
   - Middleware protection

2. **Timesheet Management** (90%+)
   - CRUD operations
   - Validation rules
   - Business logic
   - User isolation

3. **CSV Processing** (85%+)
   - File upload validation
   - Data processing
   - Export functionality

4. **Request Validation** (95%+)
   - All request classes
   - Business rules
   - Edge cases

### Testing Best Practices

1. **Isolation**: Each test is independent
2. **Descriptive Names**: Clear test method names
3. **Arrange-Act-Assert**: Consistent test structure
4. **Data Providers**: Parameterized testing for multiple scenarios
5. **Helper Methods**: Reusable test utilities
6. **Database State**: Fresh state for each test
7. **Error Cases**: Testing both success and failure paths

This comprehensive testing approach ensures the application's reliability and maintainability while providing confidence for future development and refactoring.
