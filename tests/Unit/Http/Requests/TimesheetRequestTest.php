<?php

namespace Tests\Unit\Http\Requests;

use App\Http\Requests\TimesheetRequest;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class TimesheetRequestTest extends TestCase
{
    #[Test]
    public function validateBusinessRules_returns_empty_array_for_valid_data(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertEmpty($errors);
    }

    #[Test]
    public function validateBusinessRules_returns_empty_when_properties_not_initialized(): void
    {
        // Create request with reflection to simulate uninitialized properties
        $reflection = new \ReflectionClass(TimesheetRequest::class);
        $request = $reflection->newInstanceWithoutConstructor();

        $errors = $request->validateBusinessRules();

        $this->assertEmpty($errors);
    }

    // Date Format Validation Tests

    #[Test]
    #[DataProvider('invalidDateProvider')]
    public function validateBusinessRules_validates_invalid_date_formats(string $invalidDate): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: $invalidDate,
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayHasKey('date', $errors);
        $this->assertEquals('Date must be a valid date', $errors['date']);
    }

    public static function invalidDateProvider(): array
    {
        return [
            'invalid format' => ['invalid-date'],
            'impossible date' => ['2024-13-45'],
            'letters in date' => ['2024-ab-cd'],
            'completely invalid' => ['not-a-date'],
            'random text' => ['hello world'],
            'special characters' => ['@#$%^&*'],
            'just numbers' => ['12345'],
            'mixed invalid' => ['abc-def-ghi'],
        ];
    }

    #[Test]
    #[DataProvider('validDateProvider')]
    public function validateBusinessRules_accepts_valid_date_formats(string $validDate): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: $validDate,
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayNotHasKey('date', $errors);
    }

    public static function validDateProvider(): array
    {
        return [
            'standard format' => ['2024-06-15'],
            'leap year Feb 29' => ['2024-02-29'],
            'start of year' => ['2024-01-01'],
            'end of year' => ['2024-12-31'],
            'single digit month/day' => ['2024-01-01'],
        ];
    }

    #[Test]
    public function validateBusinessRules_allows_empty_date(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayNotHasKey('date', $errors);
    }

    // Week Number Range Validation Tests

    #[Test]
    #[DataProvider('invalidWeekNumberProvider')]
    public function validateBusinessRules_validates_week_number_boundaries(string $weekNumber): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: $weekNumber,
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayHasKey('week_number', $errors);
        $this->assertEquals('Week number must be between 1 and 53', $errors['week_number']);
    }

    public static function invalidWeekNumberProvider(): array
    {
        return [
            'zero' => ['0'],
            'negative' => ['-1'],
            'large negative' => ['-10'],
            'above maximum' => ['54'],
            'way above maximum' => ['100'],
            'very large number' => ['999'],
        ];
    }

    #[Test]
    #[DataProvider('validWeekNumberProvider')]
    public function validateBusinessRules_accepts_valid_week_numbers(string $weekNumber): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: $weekNumber,
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayNotHasKey('week_number', $errors);
    }

    public static function validWeekNumberProvider(): array
    {
        return [
            'minimum valid' => ['1'],
            'middle range' => ['25'],
            'maximum valid' => ['53'],
        ];
    }

    #[Test]
    public function validateBusinessRules_skips_week_validation_for_non_numeric(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: 'abc',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        // Should not have week_number error because is_numeric() returns false
        $this->assertArrayNotHasKey('week_number', $errors);
    }

    #[Test]
    public function validateBusinessRules_allows_empty_week_number(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayNotHasKey('week_number', $errors);
    }

    // Hours Worked Range Validation Tests

    #[Test]
    #[DataProvider('invalidHoursWorkedProvider')]
    public function validateBusinessRules_validates_hours_worked_boundaries(string $hoursWorked): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: $hoursWorked,
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayHasKey('hours_worked', $errors);
        $this->assertEquals('Hours worked must be between 0 and 24', $errors['hours_worked']);
    }

    public static function invalidHoursWorkedProvider(): array
    {
        return [
            'negative decimal' => ['-0.1'],
            'negative integer' => ['-1'],
            'large negative' => ['-8.5'],
            'above maximum decimal' => ['24.1'],
            'above maximum integer' => ['25'],
            'way above maximum' => ['48'],
            'excessive hours' => ['100'],
        ];
    }

    #[Test]
    #[DataProvider('validHoursWorkedProvider')]
    public function validateBusinessRules_accepts_valid_hours_worked(string $hoursWorked): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: $hoursWorked,
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayNotHasKey('hours_worked', $errors);
    }

    public static function validHoursWorkedProvider(): array
    {
        return [
            'minimum valid' => ['0'],
            'decimal minimum' => ['0.0'],
            'quarter hour' => ['0.25'],
            'half hour' => ['0.5'],
            'standard workday' => ['8.0'],
            'decimal hours' => ['8.5'],
            'near maximum' => ['23.99'],
            'maximum valid' => ['24'],
            'maximum decimal' => ['24.0'],
        ];
    }

    #[Test]
    public function validateBusinessRules_skips_hours_validation_for_non_numeric(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: 'eight',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        // Should not have hours_worked error because is_numeric() returns false
        $this->assertArrayNotHasKey('hours_worked', $errors);
    }

    #[Test]
    public function validateBusinessRules_allows_empty_hours_worked(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertArrayNotHasKey('hours_worked', $errors);
    }

    // Multiple Validation Errors Test

    #[Test]
    public function validateBusinessRules_returns_multiple_errors_when_multiple_fields_invalid(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '54', // Above maximum (will fail validation)
            date: 'invalid-date',
            employee_id: '123',
            hours_worked: '-1',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        $this->assertCount(3, $errors);
        $this->assertArrayHasKey('date', $errors);
        $this->assertArrayHasKey('week_number', $errors);
        $this->assertArrayHasKey('hours_worked', $errors);
    }

    #[Test]
    public function validateBusinessRules_validates_week_number_zero_as_invalid(): void
    {
        // Week number '0' should now properly fail validation after fixing the empty() bug
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '0',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        // Week number '0' should now generate a validation error
        $this->assertArrayHasKey('week_number', $errors);
        $this->assertEquals('Week number must be between 1 and 53', $errors['week_number']);
    }

    #[Test]
    public function validateBusinessRules_accepts_hours_worked_zero_after_empty_fix(): void
    {
        // Hours worked '0' should be accepted as valid after fixing the empty() bug
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();

        // Hours worked '0' should NOT generate a validation error (0 hours is valid)
        $this->assertArrayNotHasKey('hours_worked', $errors);
    }
}
