<?php

namespace Tests\Feature\Http\Requests;

use App\Models\Timesheet;
use App\Models\User;
use App\Services\SessionService;
use PHPUnit\Framework\Attributes\Test;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\IntegrationTestCase;
use DateTime;

use function Tempest\Database\query;

class TimesheetValidationTest extends IntegrationTestCase
{
    private User $user;
    private mixed $timesheetId;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user for testing
        $this->user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $this->user->save();

        // Create a timesheet for testing
        $this->timesheetId = query(Timesheet::class)
            ->insert(
                user_id: (int) $this->user->id,
                booking_year: 2023,
                week_number: 25,
                date: new DateTime('2023-06-15'),
                employee_id: 310,
                hours_worked: 8.0,
                hours_worked_type: 'REGULAR',
                created_at: new DateTime(),
                updated_at: new DateTime(),
            )
            ->execute();

        // Authenticate the user
        $sessionService = $this->container->get(SessionService::class);
        $sessionService->login((int) $this->user->id, $this->user->email, $this->user->name);
    }

    // Test Tempest #[Numeric] Attribute Validation

    #[Test]
    #[DataProvider('nonNumericFieldProvider')]
    public function controller_validates_non_numeric_values_for_numeric_fields(string $field, string $nonNumericValue): void
    {
        $validData = [
            'booking_year' => '2024',
            'week_number' => '30',
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        // Replace the specific field with non-numeric value
        $invalidData = array_merge($validData, [$field => $nonNumericValue]);

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $invalidData);

        // Should return edit form with errors, not redirect
        $response->assertOk();

        // Verify timesheet was NOT updated
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
    }

    public static function nonNumericFieldProvider(): array
    {
        return [
            'booking_year with letters' => ['booking_year', 'abc'],
            'booking_year with mixed' => ['booking_year', '2024abc'],
            'booking_year with symbols' => ['booking_year', '!@#$'],
            'week_number with letters' => ['week_number', 'week'],
            'week_number with mixed' => ['week_number', '12abc'],
            'week_number with symbols' => ['week_number', '###'],
            'employee_id with letters' => ['employee_id', 'emp'],
            'employee_id with mixed' => ['employee_id', '123abc'],
            'employee_id with symbols' => ['employee_id', '@@@'],
            'hours_worked with letters' => ['hours_worked', 'eight'],
            'hours_worked with mixed' => ['hours_worked', '8.5hrs'],
            'hours_worked with symbols' => ['hours_worked', '$$$'],
        ];
    }

    // Test Required Field Validation (Individual Fields)

    #[Test]
    #[DataProvider('requiredFieldProvider')]
    public function controller_validates_individual_required_fields(string $missingField): void
    {
        $validData = [
            'booking_year' => '2024',
            'week_number' => '30',
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        // Remove the specific required field
        unset($validData[$missingField]);

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $validData);

        // Should return edit form with errors, not redirect
        $response->assertOk();

        // Verify timesheet was NOT updated
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
    }

    public static function requiredFieldProvider(): array
    {
        return [
            'missing booking_year' => ['booking_year'],
            'missing week_number' => ['week_number'],
            'missing date' => ['date'],
            'missing employee_id' => ['employee_id'],
            'missing hours_worked' => ['hours_worked'],
            'missing hours_worked_type' => ['hours_worked_type'],
        ];
    }

    #[Test]
    #[DataProvider('emptyFieldProvider')]
    public function controller_validates_empty_string_values(string $field): void
    {
        $validData = [
            'booking_year' => '2024',
            'week_number' => '30',
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        // Set the specific field to empty string
        $invalidData = array_merge($validData, [$field => '']);

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $invalidData);

        // Should return edit form with errors, not redirect
        $response->assertOk();

        // Verify timesheet was NOT updated
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
    }

    public static function emptyFieldProvider(): array
    {
        return [
            'empty booking_year' => ['booking_year'],
            'empty week_number' => ['week_number'],
            'empty date' => ['date'],
            'empty employee_id' => ['employee_id'],
            'empty hours_worked' => ['hours_worked'],
            'empty hours_worked_type' => ['hours_worked_type'],
        ];
    }

    // Test Business Rule Validation Integration

    #[Test]
    #[DataProvider('invalidWeekNumberProvider')]
    public function controller_validates_week_number_business_rules(string $invalidWeekNumber): void
    {
        $invalidData = [
            'booking_year' => '2024',
            'week_number' => $invalidWeekNumber,
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $invalidData);

        // Should return edit form with errors, not redirect
        $response->assertOk();

        // Verify timesheet was NOT updated
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
    }

    public static function invalidWeekNumberProvider(): array
    {
        return [
            'week zero' => ['0'],
            'negative week' => ['-1'],
            'week above maximum' => ['54'],
            'large week number' => ['100'],
        ];
    }

    #[Test]
    #[DataProvider('invalidHoursWorkedProvider')]
    public function controller_validates_hours_worked_business_rules(string $invalidHours): void
    {
        $invalidData = [
            'booking_year' => '2024',
            'week_number' => '30',
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => $invalidHours,
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $invalidData);

        // Should return edit form with errors, not redirect
        $response->assertOk();

        // Verify timesheet was NOT updated
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
    }

    public static function invalidHoursWorkedProvider(): array
    {
        return [
            'negative hours' => ['-1'],
            'negative decimal' => ['-0.5'],
            'above maximum' => ['25'],
            'above maximum decimal' => ['24.1'],
            'excessive hours' => ['48'],
        ];
    }

    #[Test]
    #[DataProvider('invalidDateProvider')]
    public function controller_validates_date_business_rules(string $invalidDate): void
    {
        $invalidData = [
            'booking_year' => '2024',
            'week_number' => '30',
            'date' => $invalidDate,
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $invalidData);

        // Should return edit form with errors, not redirect
        $response->assertOk();

        // Verify timesheet was NOT updated
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
    }

    public static function invalidDateProvider(): array
    {
        return [
            'invalid format' => ['invalid-date'],
            'impossible date' => ['2024-13-45'],
            'completely invalid' => ['not-a-date'],
            'mixed invalid' => ['abc-def-ghi'],
        ];
    }

    // Test Validation Layer Interaction

    #[Test]
    public function controller_processes_validation_in_correct_order(): void
    {
        // Test data that would fail at controller level (empty required field)
        // but also has business rule violations
        $invalidData = [
            'booking_year' => '', // Fails controller validation (required)
            'week_number' => '0', // Would fail business rules (invalid range)
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $invalidData);

        // Should return edit form with errors, not redirect
        $response->assertOk();

        // Verify timesheet was NOT updated
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
    }

    #[Test]
    public function controller_accepts_boundary_valid_values(): void
    {
        $boundaryValidData = [
            'booking_year' => '2024',
            'week_number' => '1', // Minimum valid
            'date' => '2024-02-29', // Leap year valid date
            'employee_id' => '1',
            'hours_worked' => '24', // Maximum valid
            'hours_worked_type' => 'OVERTIME',
        ];

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $boundaryValidData);

        // Should redirect on success
        $response->assertRedirect();

        // Verify timesheet was updated
        $updatedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2024, $updatedTimesheet->booking_year);
        $this->assertEquals(1, $updatedTimesheet->week_number);
        $this->assertEquals('2024-02-29', $updatedTimesheet->date->format('Y-m-d'));
        $this->assertEquals(1, $updatedTimesheet->employee_id);
        $this->assertEquals(24.0, $updatedTimesheet->hours_worked);
        $this->assertEquals('OVERTIME', $updatedTimesheet->hours_worked_type);
    }

    #[Test]
    public function controller_handles_whitespace_in_numeric_fields(): void
    {
        $whitespaceData = [
            'booking_year' => ' 2024 ',
            'week_number' => ' 30 ',
            'date' => '2024-07-20',
            'employee_id' => ' 320 ',
            'hours_worked' => ' 7.5 ',
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$this->timesheetId}", $whitespaceData);

        // Should redirect on success (whitespace should be handled)
        $response->assertRedirect();

        // Verify timesheet was updated with trimmed values
        $updatedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $this->timesheetId)
            ->first();

        $this->assertEquals(2024, $updatedTimesheet->booking_year);
        $this->assertEquals(30, $updatedTimesheet->week_number);
        $this->assertEquals(320, $updatedTimesheet->employee_id);
        $this->assertEquals(7.5, $updatedTimesheet->hours_worked);
    }
}
