<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Timesheet;
use App\Models\User;
use App\Services\SessionService;
use PHPUnit\Framework\Attributes\Test;
use Tests\IntegrationTestCase;
use DateTime;

use function Tempest\Database\query;

class TimesheetControllerTest extends IntegrationTestCase
{

    #[Test]
    public function can_update_a_timesheet(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Create a timesheet for the user using Tempest patterns
        $timesheetId = query(Timesheet::class)
            ->insert(
                user_id: (int) $user->id,
                booking_year: 2023,
                week_number: 25,
                date: new DateTime('2023-06-15'),
                employee_id: 310,
                hours_worked: 8.0,
                hours_worked_type: 'G',
                created_at: new DateTime(),
                updated_at: new DateTime(),
            )
            ->execute();

        // Authenticate the user using SessionService (equivalent to actingAs)
        $sessionService = $this->container->get(SessionService::class);
        $sessionService->login((int) $user->id, $user->email, $user->name);

        // Test timesheet update (CSRF automatically handled)
        $updateData = [
            'booking_year' => '2024',
            'week_number' => '30',
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'TZFG100',
        ];

        $response = $this->http->post("/timesheets/{$timesheetId}", $updateData);

        // Check that it redirects (status code 302)
        // Since the redirect includes query parameters, we'll just verify it's a redirect
        $response->assertRedirect(); // This should work like in our other tests

        // Verify the timesheet was updated in the database
        $updatedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $timesheetId)
            ->first();

        $this->assertNotNull($updatedTimesheet);
        $this->assertEquals(2024, $updatedTimesheet->booking_year);
        $this->assertEquals(30, $updatedTimesheet->week_number);
        $this->assertEquals('2024-07-20', $updatedTimesheet->date->format('Y-m-d'));
        $this->assertEquals(320, $updatedTimesheet->employee_id);
        $this->assertEquals(7.5, $updatedTimesheet->hours_worked);
        $this->assertEquals('TZFG100', $updatedTimesheet->hours_worked_type);
    }

    #[Test]
    public function can_not_update_if_not_all_fields_are_given(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Create a timesheet for the user using Tempest patterns
        $timesheetId = query(Timesheet::class)
            ->insert(
                user_id: (int) $user->id,
                booking_year: 2023,
                week_number: 25,
                date: new DateTime('2023-06-15'),
                employee_id: 310,
                hours_worked: 8.0,
                hours_worked_type: 'G',
                created_at: new DateTime(),
                updated_at: new DateTime(),
            )
            ->execute();

        // Authenticate the user using SessionService (equivalent to actingAs)
        $sessionService = $this->container->get(SessionService::class);
        $sessionService->login((int) $user->id, $user->email, $user->name);

        // Test timesheet update with incomplete data (CSRF automatically handled)
        $incompleteData = [
            'booking_year' => '2024',
            // Missing required fields: week_number, date, employee_id, hours_worked, hours_worked_type
        ];

        $response = $this->http->post("/timesheets/{$timesheetId}", $incompleteData);

        // Should return the edit form with errors, not redirect
        $response->assertOk();

        // The TimesheetController returns a GenericView with errors
        // Let's verify the response contains the view name and check database state instead
        // Since GenericView might not render properly in tests, we'll focus on database verification

        // Verify the timesheet was NOT updated in the database
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $timesheetId)
            ->first();

        $this->assertNotNull($unchangedTimesheet);
        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
        $this->assertEquals(25, $unchangedTimesheet->week_number);
        $this->assertEquals('2023-06-15', $unchangedTimesheet->date->format('Y-m-d'));
        $this->assertEquals(310, $unchangedTimesheet->employee_id);
        $this->assertEquals(8.0, $unchangedTimesheet->hours_worked);
        $this->assertEquals('G', $unchangedTimesheet->hours_worked_type);
    }

    #[Test]
    public function validates_non_numeric_booking_year(): void
    {
        [$user, $timesheetId] = $this->createUserAndTimesheet();

        $invalidData = [
            'booking_year' => 'invalid',
            'week_number' => '30',
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$timesheetId}", $invalidData);

        $response->assertOk(); // Should return form with errors
        $this->assertTimesheetUnchanged($timesheetId);
    }

    #[Test]
    public function validates_week_number_boundaries(): void
    {
        [$user, $timesheetId] = $this->createUserAndTimesheet();

        // Test week number 0 (invalid)
        $invalidData = [
            'booking_year' => '2024',
            'week_number' => '0',
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$timesheetId}", $invalidData);

        $response->assertOk(); // Should return form with errors
        $this->assertTimesheetUnchanged($timesheetId);

        // Test week number 54 (invalid)
        $invalidData['week_number'] = '54';
        $response = $this->http->post("/timesheets/{$timesheetId}", $invalidData);

        $response->assertOk(); // Should return form with errors
        $this->assertTimesheetUnchanged($timesheetId);
    }

    #[Test]
    public function validates_hours_worked_boundaries(): void
    {
        [$user, $timesheetId] = $this->createUserAndTimesheet();

        // Test negative hours (invalid)
        $invalidData = [
            'booking_year' => '2024',
            'week_number' => '30',
            'date' => '2024-07-20',
            'employee_id' => '320',
            'hours_worked' => '-1',
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$timesheetId}", $invalidData);

        $response->assertOk(); // Should return form with errors
        $this->assertTimesheetUnchanged($timesheetId);

        // Test hours above 24 (invalid)
        $invalidData['hours_worked'] = '25';
        $response = $this->http->post("/timesheets/{$timesheetId}", $invalidData);

        $response->assertOk(); // Should return form with errors
        $this->assertTimesheetUnchanged($timesheetId);
    }

    #[Test]
    public function validates_invalid_date_format(): void
    {
        [$user, $timesheetId] = $this->createUserAndTimesheet();

        $invalidData = [
            'booking_year' => '2024',
            'week_number' => '30',
            'date' => 'invalid-date',
            'employee_id' => '320',
            'hours_worked' => '7.5',
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$timesheetId}", $invalidData);

        $response->assertOk(); // Should return form with errors
        $this->assertTimesheetUnchanged($timesheetId);
    }

    #[Test]
    public function accepts_boundary_valid_values(): void
    {
        [$user, $timesheetId] = $this->createUserAndTimesheet();

        $boundaryValidData = [
            'booking_year' => '2024',
            'week_number' => '1', // Minimum valid
            'date' => '2024-07-20',
            'employee_id' => '1',
            'hours_worked' => '0.0', // Minimum valid (use decimal to ensure it's treated as numeric)
            'hours_worked_type' => 'REGULAR',
        ];

        $response = $this->http->post("/timesheets/{$timesheetId}", $boundaryValidData);

        $response->assertRedirect(); // Should succeed

        // Verify timesheet was updated
        $updatedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $timesheetId)
            ->first();

        $this->assertEquals(2024, $updatedTimesheet->booking_year);
        $this->assertEquals(1, $updatedTimesheet->week_number);
        $this->assertEquals(1, $updatedTimesheet->employee_id);
        $this->assertEquals(0.0, $updatedTimesheet->hours_worked);

        // Test maximum valid values
        $maxValidData = [
            'booking_year' => '2024',
            'week_number' => '53', // Maximum valid
            'date' => '2024-07-20',
            'employee_id' => '999',
            'hours_worked' => '24', // Maximum valid
            'hours_worked_type' => 'OVERTIME',
        ];

        $response = $this->http->post("/timesheets/{$timesheetId}", $maxValidData);

        $response->assertRedirect(); // Should succeed

        // Verify timesheet was updated
        $updatedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $timesheetId)
            ->first();

        $this->assertEquals(53, $updatedTimesheet->week_number);
        $this->assertEquals(24.0, $updatedTimesheet->hours_worked);
    }

    /**
     * Helper method to create user and timesheet for testing
     */
    private function createUserAndTimesheet(): array
    {
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        $timesheetId = query(Timesheet::class)
            ->insert(
                user_id: (int) $user->id,
                booking_year: 2023,
                week_number: 25,
                date: new DateTime('2023-06-15'),
                employee_id: 310,
                hours_worked: 8.0,
                hours_worked_type: 'REGULAR',
                created_at: new DateTime(),
                updated_at: new DateTime(),
            )
            ->execute();

        $sessionService = $this->container->get(SessionService::class);
        $sessionService->login((int) $user->id, $user->email, $user->name);

        return [$user, $timesheetId];
    }

    /**
     * Helper method to assert timesheet remains unchanged
     */
    private function assertTimesheetUnchanged(mixed $timesheetId): void
    {
        $unchangedTimesheet = query(Timesheet::class)
            ->select()
            ->where('id = ?', $timesheetId)
            ->first();

        $this->assertNotNull($unchangedTimesheet);
        $this->assertEquals(2023, $unchangedTimesheet->booking_year); // Should remain unchanged
        $this->assertEquals(25, $unchangedTimesheet->week_number);
        $this->assertEquals('2023-06-15', $unchangedTimesheet->date->format('Y-m-d'));
        $this->assertEquals(310, $unchangedTimesheet->employee_id);
        $this->assertEquals(8.0, $unchangedTimesheet->hours_worked);
        $this->assertEquals('REGULAR', $unchangedTimesheet->hours_worked_type);
    }
}
