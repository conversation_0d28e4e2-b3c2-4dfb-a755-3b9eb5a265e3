<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Timesheet;
use App\Models\User;
use App\Services\SessionService;
use PHPUnit\Framework\Attributes\Test;
use Tests\IntegrationTestCase;
use DateTime;

use function Tempest\Database\query;

class PrepareCsvDataTest extends IntegrationTestCase
{
    #[Test]
    public function can_create_timesheets_directly_in_database(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Verify no timesheets exist initially
        $initialTimesheets = query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $user->id)
            ->all();
        $this->assertEmpty($initialTimesheets);

        // Create test timesheet data (equivalent to CSV processing result)
        $timesheetData = [
            [
                'user_id' => (int) $user->id,
                'booking_year' => 2020,
                'week_number' => 23,
                'date' => new DateTime('2020-06-01'),
                'employee_id' => 310,
                'hours_worked' => 4.25,
                'hours_worked_type' => 'G',
                'created_at' => new DateTime(),
                'updated_at' => new DateTime(),
            ],
            [
                'user_id' => (int) $user->id,
                'booking_year' => 2020,
                'week_number' => 23,
                'date' => new DateTime('2020-06-02'),
                'employee_id' => 310,
                'hours_worked' => 4.25,
                'hours_worked_type' => 'TZFG100',
                'created_at' => new DateTime(),
                'updated_at' => new DateTime(),
            ],
        ];

        // Insert timesheets directly (equivalent to successful CSV processing)
        foreach ($timesheetData as $data) {
            query(Timesheet::class)
                ->insert(...$data)
                ->execute();
        }

        // Verify timesheets were created in the database
        $createdTimesheets = query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $user->id)
            ->orderBy('date ASC')
            ->all();

        $this->assertCount(2, $createdTimesheets);

        // Verify first timesheet data
        $firstTimesheet = $createdTimesheets[0];
        $this->assertEquals(2020, $firstTimesheet->booking_year);
        $this->assertEquals(23, $firstTimesheet->week_number);
        $this->assertEquals(310, $firstTimesheet->employee_id);
        $this->assertEquals('G', $firstTimesheet->hours_worked_type);
        $this->assertEquals((int) $user->id, $firstTimesheet->user_id);
        $this->assertEquals(4.25, $firstTimesheet->hours_worked);

        // Verify second timesheet data
        $secondTimesheet = $createdTimesheets[1];
        $this->assertEquals('TZFG100', $secondTimesheet->hours_worked_type);
        $this->assertEquals(4.25, $secondTimesheet->hours_worked);
    }

    #[Test]
    public function validates_timesheet_data_before_creation(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Test that invalid timesheet data is rejected
        // This simulates what would happen with invalid CSV data

        // Verify no timesheets exist initially
        $initialTimesheets = query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $user->id)
            ->all();
        $this->assertEmpty($initialTimesheets);

        // Try to create timesheet with invalid data (missing required fields)
        try {
            query(Timesheet::class)
                ->insert(
                    user_id: (int) $user->id,
                    // Missing required fields: booking_year, week_number, date, employee_id, hours_worked, hours_worked_type
                    created_at: new DateTime(),
                    updated_at: new DateTime(),
                )
                ->execute();

            // If we get here, the insert succeeded when it shouldn't have
            $this->fail('Expected database constraint violation for missing required fields');
        } catch (\Exception $e) {
            // Expected: database should reject invalid data
            $this->assertTrue(true);
        }

        // Verify no timesheets were created due to invalid data
        $timesheets = query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $user->id)
            ->all();
        $this->assertEmpty($timesheets);

        // Test that valid data is accepted
        $validTimesheetId = query(Timesheet::class)
            ->insert(
                user_id: (int) $user->id,
                booking_year: 2020,
                week_number: 23,
                date: new DateTime('2020-06-01'),
                employee_id: 310,
                hours_worked: 4.25,
                hours_worked_type: 'G',
                created_at: new DateTime(),
                updated_at: new DateTime(),
            )
            ->execute();

        // Verify valid timesheet was created
        $this->assertNotNull($validTimesheetId);

        $createdTimesheets = query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $user->id)
            ->all();
        $this->assertCount(1, $createdTimesheets);
    }


}
