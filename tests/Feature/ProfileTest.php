<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\SessionService;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

class ProfileTest extends IntegrationTestCase
{

    public function test_user_dashboard_page_is_displayed(): void
    {
        // Create a user using Tempest patterns (equivalent to factory)
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Authenticate the user using SessionService (equivalent to actingAs)
        $sessionService = $this->container->get(SessionService::class);
        $sessionService->login((int) $user->id, $user->email, $user->name);

        // Test that authenticated user can access dashboard (equivalent to profile page)
        $response = $this->http->get('/dashboard');
        $response->assertOk();

        // Verify user is still authenticated after request
        $this->assertTrue($sessionService->isAuthenticated());
        $this->assertEquals((int) $user->id, $sessionService->getUserId());
    }

    public function test_user_information_can_be_updated_in_database(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Original Name',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Verify original data
        $this->assertEquals('Original Name', $user->name);
        $this->assertEquals('<EMAIL>', $user->email);

        // Update user information directly in database (equivalent to profile update)
        query(User::class)
            ->update(
                name: 'Updated Name',
                email: '<EMAIL>',
                updated_at: new \DateTime(),
            )
            ->where('id = ?', $user->id)
            ->execute();

        // Retrieve updated user from database
        $updatedUser = query(User::class)
            ->select()
            ->where('id = ?', $user->id)
            ->first();

        // Verify updates were applied
        $this->assertNotNull($updatedUser);
        $this->assertEquals('Updated Name', $updatedUser->name);
        $this->assertEquals('<EMAIL>', $updatedUser->email);
        $this->assertEquals((int) $user->id, (int) $updatedUser->id);
    }

    public function test_user_data_consistency_is_maintained_across_operations(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Store original data
        $originalId = $user->id;
        $originalEmail = $user->email;
        $originalPassword = $user->password;

        // Update only the name (equivalent to partial profile update)
        query(User::class)
            ->update(
                name: 'Updated Name',
                updated_at: new \DateTime(),
            )
            ->where('id = ?', $user->id)
            ->execute();

        // Retrieve user and verify data consistency
        $updatedUser = query(User::class)
            ->select()
            ->where('id = ?', $user->id)
            ->first();

        // Verify that unchanged fields remain the same
        $this->assertNotNull($updatedUser);
        $this->assertEquals((int) $originalId, (int) $updatedUser->id);
        $this->assertEquals($originalEmail, $updatedUser->email);
        $this->assertEquals($originalPassword, $updatedUser->password);

        // Verify that only the name was updated
        $this->assertEquals('Updated Name', $updatedUser->name);
        $this->assertNotEquals('Test User', $updatedUser->name);
    }

    public function test_user_can_be_deleted_from_database(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Verify user exists in database
        $existingUser = query(User::class)
            ->select()
            ->where('id = ?', $user->id)
            ->first();
        $this->assertNotNull($existingUser);

        // Authenticate user first
        $sessionService = $this->container->get(SessionService::class);
        $sessionService->login((int) $user->id, $user->email, $user->name);
        $this->assertTrue($sessionService->isAuthenticated());

        // Delete user from database (equivalent to account deletion)
        query(User::class)
            ->delete()
            ->where('id = ?', $user->id)
            ->execute();

        // Verify user no longer exists in database
        $deletedUser = query(User::class)
            ->select()
            ->where('id = ?', $user->id)
            ->first();
        $this->assertNull($deletedUser);

        // Logout user after deletion (equivalent to assertGuest)
        $sessionService->logout();
        $this->assertFalse($sessionService->isAuthenticated());
    }

    public function test_correct_password_must_be_verified_for_sensitive_operations(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('correct-password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Test password verification for sensitive operations (equivalent to delete account validation)

        // Verify correct password passes validation
        $this->assertTrue(password_verify('correct-password', $user->password));

        // Verify wrong password fails validation
        $this->assertFalse(password_verify('wrong-password', $user->password));

        // Test that user can authenticate with correct password
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'correct-password',
        ]);
        $response->assertRedirect();

        // Verify user is authenticated
        $sessionService = $this->container->get(SessionService::class);
        $this->assertTrue($sessionService->isAuthenticated());

        // Test that user cannot authenticate with wrong password
        $sessionService->logout();
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'wrong-password',
        ]);
        $response->assertOk();
        $response->assertSee('Invalid credentials');

        // Verify user is not authenticated after failed login
        $this->assertFalse($sessionService->isAuthenticated());

        // Verify user still exists in database (equivalent to assertNotNull user->fresh())
        $existingUser = query(User::class)
            ->select()
            ->where('id = ?', $user->id)
            ->first();
        $this->assertNotNull($existingUser);
    }
}
