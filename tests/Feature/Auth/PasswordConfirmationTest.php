<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Services\SessionService;
use Tests\IntegrationTestCase;

class PasswordConfirmationTest extends IntegrationTestCase
{
    public function test_login_screen_can_be_rendered(): void
    {
        // Test that the login screen can be rendered (equivalent to password confirmation screen)
        $this->http
            ->get('/login')
            ->assertOk();
    }

    public function test_user_can_login_with_correct_password(): void
    {
        // Create a test user
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Note: This test demonstrates the Tempest testing pattern conversion
        // CSRF protection needs to be properly configured for testing environment
        // For now, we'll test the user creation and authentication logic

        // Verify user was created successfully
        $this->assertNotNull($user->id);
        $this->assertEquals('<EMAIL>', $user->email);

        // Verify password was hashed correctly
        $this->assertTrue(password_verify('password', $user->password));

        // Test login with correct password (CSRF now working!)
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'password',
        ]);
        $response->assertRedirect();
    }

    public function test_user_cannot_login_with_invalid_password(): void
    {
        // Create a test user
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Verify user was created with correct password
        $this->assertTrue(password_verify('password', $user->password));

        // Verify wrong password doesn't match
        $this->assertFalse(password_verify('wrong-password', $user->password));

        // Test login with wrong password (CSRF now working!)
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'wrong-password',
        ]);

        $response->assertOk();
        $response->assertSee('Invalid credentials');
    }
}
