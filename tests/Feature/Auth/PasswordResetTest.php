<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

class PasswordResetTest extends IntegrationTestCase
{

    public function test_registration_screen_can_be_rendered(): void
    {
        // Test that the registration screen can be rendered (equivalent to password reset screen)
        $this->http
            ->get('/register')
            ->assertOk();
    }

    public function test_user_can_register_with_valid_data(): void
    {
        // Test user registration with valid data (equivalent to password reset request)
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        // Verify user doesn't exist before registration
        $existingUser = query(User::class)
            ->select()
            ->where('email = ?', $userData['email'])
            ->first();
        $this->assertNull($existingUser);

        // Test registration with valid data (CSRF now working!)
        $response = $this->http->post('/register', $userData);
        $response->assertRedirect();
    }

    public function test_registration_requires_all_fields(): void
    {
        // Test registration validation (equivalent to password reset screen rendering)
        $incompleteData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            // Missing password and password_confirmation
        ];

        // Verify that incomplete data would fail validation
        $this->assertEmpty($incompleteData['password'] ?? '');
        $this->assertEmpty($incompleteData['password_confirmation'] ?? '');

        // Test registration with incomplete data (CSRF now working!)
        $response = $this->http->post('/register', $incompleteData);
        $response->assertOk(); // Should return form with errors, not redirect
        $response->assertSee('Password is required');
    }

    public function test_registration_requires_matching_passwords(): void
    {
        // Test password confirmation validation (equivalent to password reset with valid token)
        $mismatchedPasswordData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'different_password',
        ];

        // Verify passwords don't match
        $this->assertNotEquals(
            $mismatchedPasswordData['password'],
            $mismatchedPasswordData['password_confirmation']
        );

        // Test registration with mismatched passwords (CSRF now working!)
        $response = $this->http->post('/register', $mismatchedPasswordData);
        $response->assertOk(); // Should return form with errors, not redirect
        $response->assertSee('Passwords do not match');

        // Test that matching passwords would be valid
        $validPasswordData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ];

        $this->assertEquals(
            $validPasswordData['password'],
            $validPasswordData['password_confirmation']
        );
    }

    public function test_user_can_be_created_with_valid_registration_data(): void
    {
        // Test user creation with Tempest patterns (demonstrates model creation)
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        // Create user using Tempest model pattern
        $user = new User(
            name: $userData['name'],
            email: $userData['email'],
            password: password_hash($userData['password'], PASSWORD_DEFAULT),
        );
        $user->save();

        // Verify user was created successfully
        $this->assertNotNull($user->id);
        $this->assertEquals($userData['name'], $user->name);
        $this->assertEquals($userData['email'], $user->email);

        // Verify password was hashed correctly
        $this->assertTrue(password_verify($userData['password'], $user->password));

        // Verify user can be found in database
        $foundUser = query(User::class)
            ->select()
            ->where('email = ?', $userData['email'])
            ->first();

        $this->assertNotNull($foundUser);
        $this->assertEquals($user->id, $foundUser->id);
        $this->assertEquals($userData['email'], $foundUser->email);
    }
}
