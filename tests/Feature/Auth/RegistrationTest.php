<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Services\SessionService;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

class RegistrationTest extends IntegrationTestCase
{

    public function test_registration_screen_can_be_rendered(): void
    {
        $this->http
            ->get('/register')
            ->assertOk();
    }

    public function test_new_users_can_register(): void
    {
        // Verify user doesn't exist before registration
        $existingUser = query(User::class)
            ->select()
            ->where('email = ?', '<EMAIL>')
            ->first();
        $this->assertNull($existingUser);

        // Test registration with valid data (CSRF automatically handled)
        $response = $this->http->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);

        // Check that it redirects to dashboard (may include query parameters)
        $response->assertRedirect();

        // Verify user was created in database
        $createdUser = query(User::class)
            ->select()
            ->where('email = ?', '<EMAIL>')
            ->first();
        $this->assertNotNull($createdUser);
        $this->assertEquals('Test User', $createdUser->name);
        $this->assertEquals('<EMAIL>', $createdUser->email);

        // Verify password was hashed correctly
        $this->assertTrue(password_verify('password', $createdUser->password));

        // Verify user is authenticated after registration
        $sessionService = $this->container->get(SessionService::class);
        $this->assertTrue($sessionService->isAuthenticated());
        $this->assertEquals((int) $createdUser->id, $sessionService->getUserId());
    }
}
