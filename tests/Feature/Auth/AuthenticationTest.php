<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Services\SessionService;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

class AuthenticationTest extends IntegrationTestCase
{

    public function test_login_screen_can_be_rendered(): void
    {
        $this->http
            ->get('/login')
            ->assertOk();
    }

    public function test_users_can_authenticate_using_the_login_screen(): void
    {
        // Create a test user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Test login with correct credentials (CSRF automatically handled)
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'password',
        ]);

        // Check that it redirects to dashboard (may include query parameters)
        $response->assertRedirect();

        // Verify user is authenticated by checking session
        $sessionService = $this->container->get(SessionService::class);
        $this->assertTrue($sessionService->isAuthenticated());
    }

    public function test_users_can_not_authenticate_with_invalid_password(): void
    {
        // Create a test user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Test login with wrong password (CSRF automatically handled)
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'wrong-password',
        ]);

        // Should return login page with error, not redirect to dashboard
        $response->assertOk();
        $response->assertSee('Invalid credentials');

        // Verify user is NOT authenticated
        $sessionService = $this->container->get(SessionService::class);
        $this->assertFalse($sessionService->isAuthenticated());
    }

    public function test_users_can_logout(): void
    {
        // Create and authenticate a test user
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Manually authenticate the user using SessionService
        $sessionService = $this->container->get(SessionService::class);
        $sessionService->login((int) $user->id, $user->email, $user->name);

        // Verify user is authenticated before logout
        $this->assertTrue($sessionService->isAuthenticated());

        // Test logout (CSRF automatically handled)
        $response = $this->http->post('/logout');

        // Check that it redirects to home (may include query parameters)
        $response->assertRedirect();

        // Verify user is no longer authenticated
        $this->assertFalse($sessionService->isAuthenticated());
    }
}
