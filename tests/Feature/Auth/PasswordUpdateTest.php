<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Services\SessionService;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

class PasswordUpdateTest extends IntegrationTestCase
{

    public function test_user_password_can_be_verified_correctly(): void
    {
        // Create a user with a known password using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('test-password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Test that the correct password verifies successfully
        $this->assertTrue(password_verify('test-password', $user->password));

        // Test that an incorrect password fails verification
        $this->assertFalse(password_verify('wrong-password', $user->password));

        // Test that the user can login with the correct password (integration test)
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'test-password',
        ]);

        $response->assertRedirect();

        // Verify user is authenticated after login
        $sessionService = $this->container->get(SessionService::class);
        $this->assertTrue($sessionService->isAuthenticated());
        $this->assertEquals((int) $user->id, $sessionService->getUserId());
    }

    public function test_correct_password_must_be_provided_for_authentication(): void
    {
        // Create a user with a known password using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('correct-password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Test that login fails with wrong password
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'wrong-password',
        ]);

        // Should return login page with error, not redirect to dashboard
        $response->assertOk();
        $response->assertSee('Invalid credentials');

        // Verify user is NOT authenticated
        $sessionService = $this->container->get(SessionService::class);
        $this->assertFalse($sessionService->isAuthenticated());

        // Test that login succeeds with correct password
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'correct-password',
        ]);

        $response->assertRedirect();

        // Verify user IS authenticated after correct login
        $this->assertTrue($sessionService->isAuthenticated());
        $this->assertEquals((int) $user->id, $sessionService->getUserId());
    }

    public function test_password_hashing_works_correctly_during_registration(): void
    {
        // Test that registration properly hashes passwords
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'plain-text-password',
            'password_confirmation' => 'plain-text-password',
        ];

        // Register a new user (CSRF automatically handled)
        $response = $this->http->post('/register', $userData);
        $response->assertRedirect();

        // Verify user was created in database
        $createdUser = query(User::class)
            ->select()
            ->where('email = ?', $userData['email'])
            ->first();

        $this->assertNotNull($createdUser);

        // Verify password was hashed (not stored as plain text)
        $this->assertNotEquals($userData['password'], $createdUser->password);

        // Verify the hashed password can be verified with the original password
        $this->assertTrue(password_verify($userData['password'], $createdUser->password));

        // Verify wrong password doesn't verify
        $this->assertFalse(password_verify('wrong-password', $createdUser->password));

        // Verify user is authenticated after registration
        $sessionService = $this->container->get(SessionService::class);
        $this->assertTrue($sessionService->isAuthenticated());
        $this->assertEquals((int) $createdUser->id, $sessionService->getUserId());
    }
}
