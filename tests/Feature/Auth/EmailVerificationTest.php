<?php

namespace Tests\Feature\Auth;

use App\Models\User;
use App\Services\SessionService;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

class EmailVerificationTest extends IntegrationTestCase
{

    public function test_authenticated_dashboard_screen_can_be_rendered(): void
    {
        // Create a user using Tempest patterns (equivalent to unverified user)
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Authenticate the user using SessionService (equivalent to actingAs)
        $sessionService = $this->container->get(SessionService::class);
        $sessionService->login((int) $user->id, $user->email, $user->name);

        // Test that authenticated user can access dashboard (equivalent to email verification screen)
        $response = $this->http->get('/dashboard');
        $response->assertOk();

        // Verify user is still authenticated after request
        $this->assertTrue($sessionService->isAuthenticated());
        $this->assertEquals((int) $user->id, $sessionService->getUserId());
    }

    public function test_user_authentication_can_be_verified_and_persists(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Test login process (equivalent to email verification)
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'password',
        ]);

        // Should redirect to dashboard (equivalent to verification success)
        $response->assertRedirect();

        // Verify user is authenticated (equivalent to hasVerifiedEmail)
        $sessionService = $this->container->get(SessionService::class);
        $this->assertTrue($sessionService->isAuthenticated());
        $this->assertEquals((int) $user->id, $sessionService->getUserId());

        $userData = $sessionService->getUser();
        $this->assertNotNull($userData);
        $this->assertEquals($user->email, $userData['email']);

        // Verify user can access protected routes after authentication
        $dashboardResponse = $this->http->get('/dashboard');
        $dashboardResponse->assertOk();

        // Verify authentication persists across requests
        $this->assertTrue($sessionService->isAuthenticated());
    }

    public function test_user_authentication_fails_with_invalid_credentials(): void
    {
        // Create a user using Tempest patterns
        $user = new User(
            name: 'Test User',
            email: '<EMAIL>',
            password: password_hash('correct-password', PASSWORD_DEFAULT),
        );
        $user->save();

        // Test login with wrong password (equivalent to invalid hash)
        $response = $this->http->post('/login', [
            'email' => $user->email,
            'password' => 'wrong-password',
        ]);

        // Should return login page with error, not redirect
        $response->assertOk();
        $response->assertSee('Invalid credentials');

        // Verify user is NOT authenticated (equivalent to hasVerifiedEmail = false)
        $sessionService = $this->container->get(SessionService::class);
        $this->assertFalse($sessionService->isAuthenticated());
        $this->assertNull($sessionService->getUserId());

        // Verify user cannot access protected routes
        $dashboardResponse = $this->http->get('/dashboard');
        $dashboardResponse->assertRedirect('/login');

        // Verify user is still not authenticated after failed access attempt
        $this->assertFalse($sessionService->isAuthenticated());
    }

    public function test_user_data_can_be_retrieved_and_verified_from_database(): void
    {
        // Create a user using Tempest patterns
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'test-password',
        ];

        $user = new User(
            name: $userData['name'],
            email: $userData['email'],
            password: password_hash($userData['password'], PASSWORD_DEFAULT),
        );
        $user->save();

        // Verify user was created in database (equivalent to email verification data persistence)
        $retrievedUser = query(User::class)
            ->select()
            ->where('email = ?', $userData['email'])
            ->first();

        $this->assertNotNull($retrievedUser);
        $this->assertEquals($userData['name'], $retrievedUser->name);
        $this->assertEquals($userData['email'], $retrievedUser->email);
        $this->assertEquals((int) $user->id, (int) $retrievedUser->id);

        // Verify password was hashed correctly
        $this->assertTrue(password_verify($userData['password'], $retrievedUser->password));
        $this->assertNotEquals($userData['password'], $retrievedUser->password);

        // Verify user can authenticate with stored credentials
        $response = $this->http->post('/login', [
            'email' => $retrievedUser->email,
            'password' => $userData['password'],
        ]);

        $response->assertRedirect();

        // Verify authentication state matches database user
        $sessionService = $this->container->get(SessionService::class);
        $this->assertTrue($sessionService->isAuthenticated());
        $this->assertEquals((int) $retrievedUser->id, $sessionService->getUserId());

        $sessionUserData = $sessionService->getUser();
        $this->assertNotNull($sessionUserData);
        $this->assertEquals($retrievedUser->email, $sessionUserData['email']);
    }
}
