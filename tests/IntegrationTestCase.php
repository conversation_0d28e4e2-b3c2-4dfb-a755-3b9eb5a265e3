<?php

declare(strict_types=1);

namespace Tests;

use Override;
use Tempest\Database\Migrations\MigrationManager;
use Tempest\Discovery\DiscoveryLocation;
use Tempest\Framework\Testing\IntegrationTest;

abstract class IntegrationTestCase extends IntegrationTest
{
    protected string $root = __DIR__ . '/../';

    #[Override]
    protected function setUp(): void
    {
        $this->discoveryLocations = [
            new DiscoveryLocation(namespace: 'Tests\\Fixtures', path: __DIR__ . '/Fixtures'),
        ];

        parent::setUp();

        // Force testing environment after container is initialized
        $appConfig = $this->container->get(\Tempest\Core\AppConfig::class);
        $reflection = new \ReflectionProperty($appConfig, 'environment');
        $reflection->setAccessible(true);
        $reflection->setValue($appConfig, \Tempest\Core\Environment::TESTING);

        $migrationManager = $this->container->get(MigrationManager::class);

        // Drop all tables and run fresh migrations for each test
        $migrationManager->dropAll();
        $migrationManager->up();
    }

    #[Override]
    protected function tearDown(): void
    {
        // Restore error and exception handlers for PHPUnit
        restore_error_handler();
        restore_exception_handler();

        parent::tearDown();
    }
}
