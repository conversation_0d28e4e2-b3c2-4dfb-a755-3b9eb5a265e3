name: <PERSON><PERSON>

on:
  push:
    branches:
      - master

  pull_request:
    types:
      - opened
      - reopened
      - synchronize
    branches:
      - master

jobs:
  testsuite:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 1
   
    - uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'

    - name: Setup Node
      uses: actions/setup-node@v4
      with:
         node-version: 20
         cache: 'yarn'

    - name: Copy .env
      run: php -r "file_exists('.env') || copy('.env.example', '.env');"

    - name: Install Dependencies
      run: |
        composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist
        yarn
        yarn build

    - name: Generate key
      run: php artisan key:generate

    - name: Directory Permissions
      run: chmod -R 777 storage bootstrap/cache

    - name: Create Database
      run: |
        mkdir -p database
        touch database/database.sqlite

    - name: Execute tests (Unit and Feature tests) via PHPUnit/Pest
      env:
        DB_CONNECTION: sqlite
        DB_DATABASE: database/database.sqlite
      run: php artisan test
