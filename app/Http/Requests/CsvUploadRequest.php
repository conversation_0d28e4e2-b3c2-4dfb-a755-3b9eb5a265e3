<?php

namespace App\Http\Requests;

use Tempest\Http\IsRequest;
use Tempest\Http\Request;

/**
 * CSV upload validation using <PERSON>mpest's native validation
 */
final class CsvUploadRequest implements Request
{
    use IsRequest;

    public function __construct(
        public array $files = [],
    ) {
    }

    /**
     * Validate CSV file upload
     */
    public function validateCsvFile(): array
    {
        $errors = [];

        if (!isset($this->files['csvFile'])) {
            $errors['csvFile'] = 'Please select a CSV file';
            return $errors;
        }

        $file = $this->files['csvFile'];

        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errors['csvFile'] = 'Please select a valid CSV file';
            return $errors;
        }

        // Validate file type
        $allowedTypes = ['text/csv', 'text/plain', 'application/csv'];
        if (!in_array($file['type'], $allowedTypes)) {
            $errors['csvFile'] = 'Please upload a CSV file';
        }

        // Validate file size (max 5MB)
        if ($file['size'] > 5 * 1024 * 1024) {
            $errors['csvFile'] = 'File size must be less than 5MB';
        }

        return $errors;
    }

    /**
     * Validate CSV file content and delimiter
     */
    public function validateCsvContent(): array
    {
        $errors = [];

        if (!isset($this->files['csvFile'])) {
            return ['csvFile' => 'Please select a CSV file'];
        }

        $file = $this->files['csvFile'];

        try {
            $content = file_get_contents($file['tmp_name']);

            if ($content === false) {
                $errors['csvFile'] = 'Unable to read CSV file';
                return $errors;
            }

            // Check for semicolon delimiter
            if (!str_contains($content, ';')) {
                $errors['csvFile'] = 'CSV file must use semicolon (;) as delimiter';
            }

            // Check if file is not empty
            if (empty(trim($content))) {
                $errors['csvFile'] = 'CSV file cannot be empty';
            }

        } catch (\Exception $e) {
            $errors['csvFile'] = 'Error reading CSV file: ' . $e->getMessage();
        }

        return $errors;
    }
}
