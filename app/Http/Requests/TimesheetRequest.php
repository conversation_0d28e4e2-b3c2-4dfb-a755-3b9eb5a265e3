<?php

namespace App\Http\Requests;

use Tempest\Http\IsRequest;
use Tempest\Http\Request;
use Tempest\Validation\Rules\Numeric;

/**
 * Timesheet form validation using <PERSON><PERSON>'s native validation
 */
final class TimesheetRequest implements Request
{
    use IsRequest;

    public function __construct(
        #[Numeric]
        public string $booking_year,

        #[Numeric]
        public string $week_number,

        public string $date,

        #[Numeric]
        public string $employee_id,

        #[Numeric]
        public string $hours_worked,

        public string $hours_worked_type,
    ) {
        //
    }

    /**
     * Additional validation for business rules
     */
    public function validateBusinessRules(): array
    {
        $errors = [];

        // Check if properties are initialized (validation passed)
        if (!isset($this->date) || !isset($this->week_number) || !isset($this->hours_worked)) {
            return $errors; // Return empty if properties not initialized
        }

        // Validate date format
        if (!empty($this->date) && !strtotime($this->date)) {
            $errors['date'] = 'Date must be a valid date';
        }

        // Validate week number range
        if ($this->week_number !== '' && is_numeric($this->week_number)) {
            $weekNum = (int) $this->week_number;
            if ($weekNum < 1 || $weekNum > 53) {
                $errors['week_number'] = 'Week number must be between 1 and 53';
            }
        }

        // Validate hours worked range
        if ($this->hours_worked !== '' && is_numeric($this->hours_worked)) {
            $hours = (float) $this->hours_worked;
            if ($hours < 0 || $hours > 24) {
                $errors['hours_worked'] = 'Hours worked must be between 0 and 24';
            }
        }

        return $errors;
    }
}
