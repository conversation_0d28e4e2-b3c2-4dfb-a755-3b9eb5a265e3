<?php

namespace App\Http\Requests;

use Tempest\Http\IsRequest;
use Tempest\Http\Request;
use Tempest\Validation\Rules\Email;
use Tempest\Validation\Rules\Length;

/**
 * Registration form validation using <PERSON>mpest's native validation
 */
final class RegisterRequest implements Request
{
    use IsRequest;

    public function __construct(
        public string $name,

        #[Email]
        public string $email,

        #[Length(min: 8)]
        public string $password,

        public string $password_confirmation,
    ) {
        //
    }

    /**
     * Validate that passwords match (custom business rule)
     */
    public function validatePasswordConfirmation(): bool
    {
        // Check if properties are initialized (validation passed)
        if (!isset($this->password) || !isset($this->password_confirmation)) {
            return false;
        }

        return $this->password === $this->password_confirmation;
    }
}
