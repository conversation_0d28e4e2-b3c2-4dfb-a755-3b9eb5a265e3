<?php

namespace App\Models;

use Tempest\Database\IsDatabaseModel;
use Tempest\Database\BelongsTo;
use DateTime;

final class Timesheet
{
    use IsDatabaseModel;

    public int $user_id;

    public int $booking_year;

    public int $week_number;

    public DateTime $date;

    public int $employee_id;

    public float $hours_worked;

    public string $hours_worked_type;

    public ?DateTime $created_at = null;

    public ?DateTime $updated_at = null;

    #[BelongsTo(ownerJoin: 'user_id', relationJoin: 'id')]
    public ?User $user = null;

    public function __construct(
        int $user_id = 0,
        int $booking_year = 0,
        int $week_number = 0,
        ?DateTime $date = null,
        int $employee_id = 0,
        float $hours_worked = 0.0,
        string $hours_worked_type = '',
    ) {
        $this->user_id = $user_id;
        $this->booking_year = $booking_year;
        $this->week_number = $week_number;
        $this->date = $date ?? new DateTime();
        $this->employee_id = $employee_id;
        $this->hours_worked = $hours_worked;
        $this->hours_worked_type = $hours_worked_type;
        $this->created_at = new DateTime();
        $this->updated_at = new DateTime();
    }
}
