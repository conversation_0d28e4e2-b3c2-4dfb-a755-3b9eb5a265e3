<?php

namespace App\Models;

use Tempest\Database\IsDatabaseModel;
use Tempest\Database\HasMany;
use Tempest\Validation\Rules\Email;
use Tempest\Validation\Rules\Length;
use DateTime;

final class User
{
    use IsDatabaseModel;

    #[Length(min: 1, max: 255)]
    public string $name;

    #[Email]
    #[Length(max: 255)]
    public string $email;

    public ?DateTime $email_verified_at = null;

    public string $password;

    public ?string $remember_token = null;

    public ?DateTime $created_at = null;

    public ?DateTime $updated_at = null;

    /** @var \App\Models\Timesheet[] */
    #[HasMany(relationJoin: 'user_id', ownerJoin: 'id')]
    public array $timesheets = [];

    public function __construct(
        string $name = '',
        string $email = '',
        string $password = '',
    ) {
        $this->name = $name;
        $this->email = $email;
        $this->password = $password;
        $this->created_at = new DateTime();
        $this->updated_at = new DateTime();
    }
}
