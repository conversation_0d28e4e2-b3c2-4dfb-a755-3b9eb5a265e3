<?php

namespace App\Exceptions;

use Exception;

/**
 * Exception thrown when validation fails
 */
class ValidationException extends Exception
{
    public function __construct(
        private array $errors,
        string $message = 'Validation failed',
        int $code = 422
    ) {
        parent::__construct($message, $code);
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }

    public function getFirstError(): ?string
    {
        $firstError = reset($this->errors);
        return $firstError ?: null;
    }
}
