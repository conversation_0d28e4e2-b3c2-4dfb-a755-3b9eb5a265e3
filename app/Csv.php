<?php

declare(strict_types=1);

namespace App;

use DateTime;

final class Csv
{
    public function __construct(
        public int $Boekjaar,
        public int $Week,
        public DateTime $Datum,
        public int $Persnr,
        public string $Uren,
        public string $Uurcode,
    ) {
        //
    }

    private function csvRules(): array
    {
        return [
            'Boekjaar' => [
                'required',
                'integer',
            ],

            'Week' => [
                'required',
                'integer',
            ],

            'Datum' => [
                'required',
                'date',
            ],

            'Persnr' => [
                'required',
                'integer',
            ],

            'Uren' => [
                'required',
                'numeric',
            ],

            'Uurcode' => [
                'required',
                'string',
            ],
        ];
    }

    public function validate(): bool
    {
        // Simple validation - could be enhanced with Tempest validation
        return !empty($this->Boekjaar) &&
               !empty($this->Week) &&
               !empty($this->Datum) &&
               !empty($this->Persnr) &&
               !empty($this->Uren) &&
               !empty($this->Uurcode);
    }

    public function toModelAttributes(): array
    {
        return [
            'booking_year' => $this->Boekjaar,
            'week_number' => $this->Week,
            'date' => $this->Datum,
            'employee_id' => $this->Persnr,
            'hours_worked' => (float) $this->Uren,
            'hours_worked_type' => $this->Uurcode,
        ];
    }

    public function toArray(): array
    {
        return [
            'Boekjaar' => $this->Boekjaar,
            'Week' => $this->Week,
            'Datum' => $this->Datum,
            'Persnr' => $this->Persnr,
            'Uren' => (float) $this->Uren,
            'Uurcode' => $this->Uurcode,
        ];
    }
}
