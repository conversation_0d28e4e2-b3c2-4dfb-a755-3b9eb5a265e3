<?php

namespace App\Repositories;

use App\Models\User;
use DateTime;

use function Tempest\Database\query;

/**
 * Repository for User model operations
 * Centralizes database queries and provides a clean API for user operations
 */
final readonly class UserRepository
{
    /**
     * Find user by email
     */
    public function findByEmail(string $email): ?User
    {
        return query(User::class)
            ->select()
            ->where('email = ?', $email)
            ->first();
    }

    /**
     * Find user by ID
     */
    public function findById(int $id): ?User
    {
        return query(User::class)
            ->select()
            ->where('id = ?', $id)
            ->first();
    }

    /**
     * Check if email exists
     */
    public function emailExists(string $email): bool
    {
        return $this->findByEmail($email) !== null;
    }

    /**
     * Create a new user
     */
    public function create(string $name, string $email, string $password): int
    {
        $id = query(User::class)
            ->insert(
                name: $name,
                email: $email,
                password: password_hash($password, PASSWORD_DEFAULT),
                created_at: new DateTime(),
                updated_at: new DateTime(),
            )
            ->execute();

        return (int) $id;
    }

    /**
     * Update user information
     */
    public function update(int $id, array $data): bool
    {
        $updateData = array_merge($data, ['updated_at' => new DateTime()]);

        $result = query(User::class)
            ->update(...$updateData)
            ->where('id = ?', $id)
            ->execute();

        return $result !== false;
    }

    /**
     * Delete user
     */
    public function delete(int $id): bool
    {
        $result = query(User::class)
            ->delete()
            ->where('id = ?', $id)
            ->execute();

        return $result !== false;
    }

    /**
     * Verify user password
     */
    public function verifyPassword(User $user, string $password): bool
    {
        return password_verify($password, $user->password);
    }

    /**
     * Update user password
     */
    public function updatePassword(int $id, string $newPassword): bool
    {
        return $this->update($id, [
            'password' => password_hash($newPassword, PASSWORD_DEFAULT)
        ]);
    }
}
