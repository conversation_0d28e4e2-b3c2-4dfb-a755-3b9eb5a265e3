<?php

namespace App\Repositories;

use App\Models\Timesheet;
use DateTime;

use function Tempest\Database\query;

/**
 * Repository for Timesheet model operations
 * Centralizes database queries and provides a clean API for timesheet operations
 */
final readonly class TimesheetRepository
{
    /**
     * Find timesheet by ID for specific user
     */
    public function findByIdForUser(int $id, int $userId): ?Timesheet
    {
        return query(Timesheet::class)
            ->select()
            ->where('id = ? AND user_id = ?', $id, $userId)
            ->first();
    }

    /**
     * Get paginated timesheets for user
     */
    public function getPaginatedForUser(int $userId, int $page = 1, int $perPage = 25): array
    {
        $offset = ($page - 1) * $perPage;

        $timesheets = query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $userId)
            ->orderBy('date DESC')
            ->limit($perPage)
            ->offset($offset)
            ->all();

        $totalCount = query('timesheets')
            ->count()
            ->where('user_id = ?', $userId)
            ->execute();

        return [
            'timesheets' => $timesheets,
            'totalCount' => $totalCount,
            'totalPages' => ceil($totalCount / $perPage),
            'currentPage' => $page,
            'hasNextPage' => $page < ceil($totalCount / $perPage),
            'hasPrevPage' => $page > 1,
        ];
    }

    /**
     * Get all timesheets for user (for export)
     */
    public function getAllForUser(int $userId): array
    {
        return query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $userId)
            ->orderBy('date ASC')
            ->all();
    }

    /**
     * Create a new timesheet
     */
    public function create(array $data): int
    {
        $timesheetData = array_merge($data, [
            'created_at' => new DateTime(),
            'updated_at' => new DateTime(),
        ]);

        return query(Timesheet::class)
            ->insert(...$timesheetData)
            ->execute();
    }

    /**
     * Update timesheet
     */
    public function update(int $id, array $data): bool
    {
        $updateData = array_merge($data, ['updated_at' => new DateTime()]);
        
        $result = query(Timesheet::class)
            ->update(...$updateData)
            ->where('id = ?', $id)
            ->execute();

        return $result !== false;
    }

    /**
     * Delete timesheet
     */
    public function delete(int $id): bool
    {
        $result = query(Timesheet::class)
            ->delete()
            ->where('id = ?', $id)
            ->execute();

        return $result !== false;
    }

    /**
     * Bulk create timesheets from CSV data
     */
    public function bulkCreate(array $timesheets): int
    {
        $count = 0;
        foreach ($timesheets as $timesheetData) {
            try {
                $this->create($timesheetData);
                $count++;
            } catch (\Exception $e) {
                // Log error but continue processing
                continue;
            }
        }
        return $count;
    }

    /**
     * Check if timesheet exists for user
     */
    public function existsForUser(int $id, int $userId): bool
    {
        return $this->findByIdForUser($id, $userId) !== null;
    }

    /**
     * Get timesheet statistics for user
     */
    public function getStatsForUser(int $userId): array
    {
        $totalHours = query('timesheets')
            ->select('SUM(hours_worked) as total')
            ->where('user_id = ?', $userId)
            ->first();

        $totalEntries = query('timesheets')
            ->count()
            ->where('user_id = ?', $userId)
            ->execute();

        return [
            'totalHours' => $totalHours['total'] ?? 0,
            'totalEntries' => $totalEntries,
        ];
    }
}
