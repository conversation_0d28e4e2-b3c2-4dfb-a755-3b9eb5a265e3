<?php

namespace App\Services;

use Tempest\View\GenericView;
use Tempest\View\View;

use function Tempest\view;

/**
 * Centralized view rendering service for consistent view handling
 */
final readonly class ViewService
{
    private const string VIEW_PATH = __DIR__ . '/../Views/';

    /**
     * Render an auth view with consistent path resolution
     */
    public function auth(string $template, array $data = []): View
    {
        return view(__DIR__ . "/../Views/auth/{$template}.view.php", ...$data);
    }

    /**
     * Render a dashboard view using GenericView for consistency
     * Automatically handles success/error messages from query parameters
     */
    public function dashboard(array $data = []): View
    {
        // Handle success/error messages from query parameters
        if (isset($_GET['success']) && !isset($data['success'])) {
            $data['success'] = $_GET['success'];
        }

        if (isset($_GET['error']) && !isset($data['error'])) {
            $data['error'] = $_GET['error'];
        }

        return new GenericView('dashboard', $data);
    }

    /**
     * Render a timesheet view using GenericView
     */
    public function timesheet(string $template, array $data = []): View
    {
        // Timesheet views support both individual field errors and general error messages
        // Keep the errors array for field-specific validation display
        return new GenericView("timesheet/{$template}", $data);
    }

    /**
     * Render the welcome view
     */
    public function welcome(array $data = []): View
    {
        return new GenericView('welcome', $data);
    }

    /**
     * Render any view with error handling
     */
    public function render(string $template, array $data = []): View
    {
        try {
            return new GenericView($template, $data);
        } catch (\Exception $e) {
            // Fallback to error view
            return new GenericView('error', [
                'message' => 'View not found: ' . $template,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Render view with validation errors
     */
    public function withErrors(string $template, array $errors, array $old = [], array $data = []): View
    {
        if (str_starts_with($template, 'auth/')) {
            $template = str_replace('auth/', '', $template);
            // Convert errors array to single error message for compatibility with existing views
            $errorMessage = !empty($errors) ? reset($errors) : '';
            return view(__DIR__ . "/../Views/auth/{$template}.view.php",
                ...array_merge($data, [
                    'error' => $errorMessage,
                    'errors' => $errors,
                    'old' => $old
                ])
            );
        }

        return $this->render($template, array_merge($data, [
            'errors' => $errors,
            'old' => $old
        ]));
    }

    /**
     * Render view with success message
     */
    public function withSuccess(string $template, string $message, array $data = []): View
    {
        return $this->render($template, array_merge($data, [
            'success' => $message
        ]));
    }

    /**
     * Render view with error message
     */
    public function withError(string $template, string $message, array $data = []): View
    {
        if (str_starts_with($template, 'auth/')) {
            $template = str_replace('auth/', '', $template);
            return view(__DIR__ . "/../Views/auth/{$template}.view.php",
                ...array_merge($data, [
                    'error' => $message
                ])
            );
        }

        return $this->render($template, array_merge($data, [
            'error' => $message
        ]));
    }
}
