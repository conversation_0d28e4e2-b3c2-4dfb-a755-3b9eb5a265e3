<?php

namespace App\Services;

use App\Models\User;
use Tempest\Http\Session\Session;

use function Tempest\Database\query;

/**
 * Enhanced session management service with improved type safety and user model integration
 * Handles user authentication state and provides a clean API for session operations
 */
final readonly class SessionService
{
    private const string USER_ID_KEY = 'user_id';
    private const string USER_EMAIL_KEY = 'user_email';
    private const string USER_NAME_KEY = 'user_name';
    private const string LAST_ACTIVITY_KEY = 'last_activity';

    public function __construct(private Session $session)
    {
    }

    /**
     * Check if user is authenticated and session is valid
     */
    public function isAuthenticated(): bool
    {
        $userId = $this->session->get(self::USER_ID_KEY);
        $lastActivity = $this->session->get(self::LAST_ACTIVITY_KEY);

        // Check if user ID exists and session hasn't expired (24 hours)
        if ($userId === null || empty($userId)) {
            return false;
        }

        if ($lastActivity && (time() - $lastActivity) > 86400) {
            $this->logout();
            return false;
        }

        // Update last activity
        $this->session->set(self::LAST_ACTIVITY_KEY, time());

        return true;
    }

    /**
     * Get authenticated user ID with type safety
     */
    public function getUserId(): ?int
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return (int) $this->session->get(self::USER_ID_KEY);
    }

    /**
     * Get authenticated user ID or throw exception
     */
    public function requireUserId(): int
    {
        $userId = $this->getUserId();
        if ($userId === null) {
            throw new \RuntimeException('User not authenticated');
        }
        return $userId;
    }

    /**
     * Get authenticated user data
     */
    public function getUser(): ?array
    {
        if (!$this->isAuthenticated()) {
            return null;
        }

        return [
            'id' => (int) $this->session->get(self::USER_ID_KEY),
            'name' => $this->session->get(self::USER_NAME_KEY, 'User'),
            'email' => $this->session->get(self::USER_EMAIL_KEY, ''),
        ];
    }

    /**
     * Get full user model from database
     */
    public function getUserModel(): ?User
    {
        $userId = $this->getUserId();
        if ($userId === null) {
            return null;
        }

        return query(User::class)
            ->select()
            ->where('id = ?', $userId)
            ->first();
    }

    /**
     * Authenticate user and store session data with activity tracking
     */
    public function login(int $userId, string $email, string $name = ''): void
    {
        // Store user data in session
        $this->session->set(self::USER_ID_KEY, $userId);
        $this->session->set(self::USER_EMAIL_KEY, $email);
        $this->session->set(self::USER_NAME_KEY, $name);
        $this->session->set(self::LAST_ACTIVITY_KEY, time());
    }

    /**
     * Login with User model
     */
    public function loginWithUser(User $user): void
    {
        $this->login((int) $user->id, $user->email, $user->name);
    }

    /**
     * Logout user and destroy session
     */
    public function logout(): void
    {
        // Remove user data from session
        $this->session->remove(self::USER_ID_KEY);
        $this->session->remove(self::USER_EMAIL_KEY);
        $this->session->remove(self::USER_NAME_KEY);

        // Destroy the entire session
        $this->session->destroy();
    }
}
