<?php

namespace App\Controllers;

use Tempest\Router\Get;
use Tempest\Router\StaticPage;
use Tempest\View\View;
use Tempest\View\GenericView;

/**
 * DocumentationController for generating static documentation pages
 *
 * This controller generates comprehensive documentation for the 365-manage-hours
 * timesheet management application using Tempest's static page generation feature.
 */
final readonly class DocumentationController
{
    #[StaticPage]
    #[Get('/docs')]
    public function documentation(): View
    {
        return new GenericView('documentation/test', [
            'title' => 'Test Page',
            'content' => 'This is a test page',
        ]);
    }

    #[StaticPage]
    #[Get('/docs/api')]
    public function apiDocumentation(): View
    {
        return new GenericView('documentation/api', [
            'title' => 'API Documentation - 365 Manage Hours',
            'description' => 'Complete API reference for the 365 Manage Hours application',
            'endpoints' => $this->getApiEndpoints(),
        ]);
    }

    #[StaticPage]
    #[Get('/docs/validation')]
    public function validationDocumentation(): View
    {
        return new GenericView('documentation/validation', [
            'title' => 'Validation Rules - 365 Manage Hours',
            'description' => 'Complete validation rules and business logic documentation',
            'validationRules' => $this->getValidationRules(),
        ]);
    }

    /**
     * Get all API endpoints for documentation
     */
    private function getApiEndpoints(): array
    {
        return [
            'authentication' => [
                'title' => 'Authentication',
                'endpoints' => [
                    [
                        'method' => 'GET',
                        'path' => '/login',
                        'description' => 'Display login form',
                        'middleware' => ['GuestMiddleware'],
                        'controller' => 'LoginController::show',
                    ],
                    [
                        'method' => 'POST',
                        'path' => '/login',
                        'description' => 'Authenticate user',
                        'middleware' => ['GuestMiddleware'],
                        'controller' => 'LoginController::store',
                        'request' => 'LoginRequest',
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/register',
                        'description' => 'Display registration form',
                        'middleware' => ['GuestMiddleware'],
                        'controller' => 'RegisterController::show',
                    ],
                    [
                        'method' => 'POST',
                        'path' => '/register',
                        'description' => 'Register new user',
                        'middleware' => ['GuestMiddleware'],
                        'controller' => 'RegisterController::store',
                        'request' => 'RegisterRequest',
                    ],
                    [
                        'method' => 'POST',
                        'path' => '/logout',
                        'description' => 'Logout current user',
                        'controller' => 'LoginController::destroy',
                    ],
                ],
            ],
            'dashboard' => [
                'title' => 'Dashboard',
                'endpoints' => [
                    [
                        'method' => 'GET',
                        'path' => '/dashboard',
                        'description' => 'Display user dashboard with paginated timesheets',
                        'middleware' => ['AuthMiddleware'],
                        'controller' => 'DashboardController',
                        'parameters' => ['page' => 'optional pagination page number'],
                    ],
                ],
            ],
            'timesheets' => [
                'title' => 'Timesheet Management',
                'endpoints' => [
                    [
                        'method' => 'GET',
                        'path' => '/timesheets/{id}/edit',
                        'description' => 'Display timesheet edit form',
                        'middleware' => ['AuthMiddleware'],
                        'controller' => 'TimesheetController::edit',
                        'parameters' => ['id' => 'timesheet ID'],
                    ],
                    [
                        'method' => 'POST',
                        'path' => '/timesheets/{id}',
                        'description' => 'Update existing timesheet',
                        'middleware' => ['AuthMiddleware'],
                        'controller' => 'TimesheetController::update',
                        'parameters' => ['id' => 'timesheet ID'],
                        'request' => 'TimesheetRequest',
                    ],
                ],
            ],
            'csv' => [
                'title' => 'CSV Processing',
                'endpoints' => [
                    [
                        'method' => 'POST',
                        'path' => '/process-csv',
                        'description' => 'Upload and process CSV timesheet file',
                        'middleware' => ['AuthMiddleware'],
                        'controller' => 'ProcessCsvController',
                        'request' => 'CsvUploadRequest',
                    ],
                    [
                        'method' => 'GET',
                        'path' => '/export',
                        'description' => 'Export user timesheets as CSV',
                        'middleware' => ['AuthMiddleware'],
                        'controller' => 'DownloadTimesheetController',
                    ],
                ],
            ],
        ];
    }

    /**
     * Get validation rules for documentation
     */
    private function getValidationRules(): array
    {
        return [
            'timesheet' => [
                'title' => 'Timesheet Validation',
                'description' => 'Business rules and validation for timesheet entries',
                'fields' => [
                    'booking_year' => [
                        'type' => 'string (numeric)',
                        'required' => true,
                        'validation' => ['#[Numeric]', 'Required field'],
                        'description' => 'The booking year for the timesheet entry',
                    ],
                    'week_number' => [
                        'type' => 'string (numeric)',
                        'required' => true,
                        'validation' => ['#[Numeric]', 'Range: 1-53', 'Required field'],
                        'description' => 'Week number within the booking year',
                        'business_rules' => 'Must be between 1 and 53 (ISO week numbering)',
                    ],
                    'date' => [
                        'type' => 'string (date)',
                        'required' => true,
                        'validation' => ['Valid date format (Y-m-d)', 'Required field'],
                        'description' => 'Date of the timesheet entry',
                        'business_rules' => 'Must be a valid date that can be parsed by strtotime()',
                    ],
                    'employee_id' => [
                        'type' => 'string (numeric)',
                        'required' => true,
                        'validation' => ['#[Numeric]', 'Required field'],
                        'description' => 'Employee identifier',
                    ],
                    'hours_worked' => [
                        'type' => 'string (numeric)',
                        'required' => true,
                        'validation' => ['#[Numeric]', 'Range: 0-24', 'Required field'],
                        'description' => 'Number of hours worked',
                        'business_rules' => 'Must be between 0 and 24 hours (decimal values allowed)',
                    ],
                    'hours_worked_type' => [
                        'type' => 'string',
                        'required' => true,
                        'validation' => ['Required field'],
                        'description' => 'Type of hours worked (e.g., REGULAR, OVERTIME)',
                    ],
                ],
            ],
            'authentication' => [
                'title' => 'Authentication Validation',
                'description' => 'Validation rules for user authentication',
                'login' => [
                    'email' => [
                        'type' => 'string',
                        'required' => true,
                        'validation' => ['#[Email]', 'Valid email format'],
                        'description' => 'User email address',
                    ],
                    'password' => [
                        'type' => 'string',
                        'required' => true,
                        'validation' => ['Required field'],
                        'description' => 'User password',
                    ],
                ],
                'registration' => [
                    'name' => [
                        'type' => 'string',
                        'required' => true,
                        'validation' => ['#[Length(min: 2)]', 'Minimum 2 characters'],
                        'description' => 'User full name',
                    ],
                    'email' => [
                        'type' => 'string',
                        'required' => true,
                        'validation' => ['#[Email]', 'Valid email format', 'Unique email'],
                        'description' => 'User email address',
                    ],
                    'password' => [
                        'type' => 'string',
                        'required' => true,
                        'validation' => ['#[Length(min: 8)]', 'Minimum 8 characters'],
                        'description' => 'User password',
                    ],
                    'password_confirmation' => [
                        'type' => 'string',
                        'required' => true,
                        'validation' => ['Must match password'],
                        'description' => 'Password confirmation',
                    ],
                ],
            ],
            'csv_upload' => [
                'title' => 'CSV Upload Validation',
                'description' => 'Validation rules for CSV file uploads',
                'file_validation' => [
                    'file_type' => 'Must be CSV file',
                    'file_size' => 'Maximum file size limits apply',
                    'file_structure' => 'Must contain required columns',
                ],
                'required_columns' => [
                    'Boekjaar' => 'Booking year',
                    'Week' => 'Week number',
                    'Datum' => 'Date',
                    'Persnr' => 'Employee number',
                    'Uren' => 'Hours worked',
                    'Uurcode' => 'Hour type code',
                ],
            ],
        ];
    }
}
