<?php

namespace App\Controllers;

use App\Exceptions\AuthenticationException;
use App\Services\SessionService;
use Tempest\Http\Response;
use Tempest\Http\Responses\Redirect;
use Tempest\View\View;

use function Tempest\view;

abstract readonly class BaseController
{
    public function __construct(protected SessionService $sessionService)
    {
    }



    /**
     * Redirect with success message
     */
    protected function redirectWithSuccess(string $url, string $message): Response
    {
        return new Redirect($url . '?success=' . urlencode($message));
    }

    /**
     * Redirect with error message
     */
    protected function redirectWithError(string $url, string $message): Response
    {
        return new Redirect($url . '?error=' . urlencode($message));
    }

    /**
     * Get authenticated user ID with proper exception handling
     */
    protected function getAuthenticatedUserId(): int
    {
        $userId = $this->sessionService->getUserId();
        if ($userId === null) {
            throw new AuthenticationException('User not authenticated');
        }
        return $userId;
    }

    /**
     * Require authentication and return user ID
     */
    protected function requireAuthentication(): int
    {
        return $this->getAuthenticatedUserId();
    }

    /**
     * Validate required fields
     */
    protected function validateRequired(array $data, array $requiredFields): array
    {
        $errors = [];

        foreach ($requiredFields as $field => $label) {
            if (empty($data[$field])) {
                $errors[$field] = "{$label} is required";
            }
        }

        return $errors;
    }

    /**
     * Validate numeric fields
     */
    protected function validateNumeric(array $data, array $numericFields): array
    {
        $errors = [];

        foreach ($numericFields as $field => $label) {
            if (!empty($data[$field]) && !is_numeric($data[$field])) {
                $errors[$field] = "{$label} must be a number";
            }
        }

        return $errors;
    }
}
