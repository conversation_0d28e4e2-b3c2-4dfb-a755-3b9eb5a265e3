<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Exceptions\AuthenticationException;
use App\Http\Requests\TimesheetRequest;
use App\Middleware\AuthMiddleware;
use App\Repositories\TimesheetRepository;
use App\Services\SessionService;
use App\Services\ViewService;
use Tempest\Http\Request;
use Tempest\Http\Response;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\View\View;

/**
 * Improved TimesheetController using repository pattern and centralized services
 */
final readonly class TimesheetController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private TimesheetRepository $timesheetRepository,
        private ViewService $viewService
    ) {
        parent::__construct($sessionService);
    }

    #[Get('/timesheets/{id}/edit', middleware: [AuthMiddleware::class])]
    public function edit(int $id): View|Response
    {
        try {
            $userId = $this->getAuthenticatedUserId();

            // Find timesheet using repository
            $timesheet = $this->timesheetRepository->findByIdForUser($id, $userId);

            if (!$timesheet) {
                return $this->redirectWithError('/dashboard', 'Timesheet not found');
            }

            return $this->viewService->timesheet('edit', [
                'timesheet' => $timesheet
            ]);

        } catch (AuthenticationException $e) {
            return $this->redirectWithError('/login', 'Please log in to access timesheets');
        } catch (\Exception $e) {
            return $this->redirectWithError('/dashboard', 'An error occurred while loading the timesheet');
        }
    }

    #[Post('/timesheets/{id}', middleware: [AuthMiddleware::class])]
    public function update(int $id, Request $request): View | Response
    {
        $timesheet = null;

        try {
            $userId = $this->getAuthenticatedUserId();

            // Find timesheet using repository
            $timesheet = $this->timesheetRepository->findByIdForUser($id, $userId);

            if (!$timesheet) {
                return $this->redirectWithError('/dashboard', 'Timesheet not found');
            }

            // Create TimesheetRequest for validation
            $timesheetRequest = new TimesheetRequest(
                booking_year: $request->body['booking_year'] ?? '',
                week_number: $request->body['week_number'] ?? '',
                date: $request->body['date'] ?? '',
                employee_id: $request->body['employee_id'] ?? '',
                hours_worked: $request->body['hours_worked'] ?? '',
                hours_worked_type: $request->body['hours_worked_type'] ?? ''
            );

            // Manual validation for compatibility
            $errors = $this->validateTimesheetRequest($timesheetRequest);
            if (!empty($errors)) {
                return $this->viewService->timesheet('edit', [
                    'timesheet' => $timesheet,
                    'errors' => $errors,
                    'old' => $request->body
                ]);
            }

            // Additional business rule validation
            $businessErrors = $timesheetRequest->validateBusinessRules();
            if (!empty($businessErrors)) {
                return $this->viewService->timesheet('edit', [
                    'timesheet' => $timesheet,
                    'errors' => $businessErrors,
                    'old' => $request->body
                ]);
            }

            // Prepare update data
            $updateData = [
                'booking_year' => (int) $timesheetRequest->booking_year,
                'week_number' => (int) $timesheetRequest->week_number,
                'date' => new \DateTime($timesheetRequest->date),
                'employee_id' => (int) $timesheetRequest->employee_id,
                'hours_worked' => (float) $timesheetRequest->hours_worked,
                'hours_worked_type' => $timesheetRequest->hours_worked_type,
            ];

            // Update timesheet using repository
            $success = $this->timesheetRepository->update($id, $updateData);

            if (!$success) {
                return $this->redirectWithError('/dashboard', 'Failed to update timesheet');
            }

            return $this->redirectWithSuccess('/dashboard', 'Timesheet updated successfully');

        } catch (AuthenticationException $e) {
            return $this->redirectWithError('/login', 'Please log in to update timesheets');
        } catch (\Exception $e) {
            return $this->redirectWithError('/dashboard', 'An error occurred while updating the timesheet');
        }
    }

    /**
     * Validate timesheet request manually for compatibility
     */
    private function validateTimesheetRequest(TimesheetRequest $timesheetRequest): array
    {
        $errors = [];

        if (empty($timesheetRequest->booking_year)) {
            $errors['booking_year'] = 'Booking year is required';
        } elseif (!is_numeric($timesheetRequest->booking_year)) {
            $errors['booking_year'] = 'Booking year must be a number';
        }

        if (empty($timesheetRequest->week_number)) {
            $errors['week_number'] = 'Week number is required';
        } elseif (!is_numeric($timesheetRequest->week_number)) {
            $errors['week_number'] = 'Week number must be a number';
        }

        if (empty($timesheetRequest->date)) {
            $errors['date'] = 'Date is required';
        }

        if (empty($timesheetRequest->employee_id)) {
            $errors['employee_id'] = 'Employee ID is required';
        } elseif (!is_numeric($timesheetRequest->employee_id)) {
            $errors['employee_id'] = 'Employee ID must be a number';
        }

        if (empty($timesheetRequest->hours_worked)) {
            $errors['hours_worked'] = 'Hours worked is required';
        } elseif (!is_numeric($timesheetRequest->hours_worked)) {
            $errors['hours_worked'] = 'Hours worked must be a number';
        }

        if (empty($timesheetRequest->hours_worked_type)) {
            $errors['hours_worked_type'] = 'Hours worked type is required';
        }

        return $errors;
    }




}
