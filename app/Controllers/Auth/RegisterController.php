<?php

namespace App\Controllers\Auth;

use App\Controllers\BaseController;
use App\Controllers\Routes;
use App\Http\Requests\RegisterRequest;
use App\Middleware\GuestMiddleware;
use App\Repositories\UserRepository;
use App\Services\SessionService;
use App\Services\ViewService;
use Tempest\Http\Request;
use Tempest\Http\Response;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\View\View;

/**
 * Improved RegisterController using best practices
 * Uses dependency injection, repository pattern, and centralized services
 */
final readonly class RegisterController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private UserRepository $userRepository,
        private ViewService $viewService
    ) {
        parent::__construct($sessionService);
    }
    #[Get(Routes::REGISTER, middleware: [GuestMiddleware::class])]
    public function show(): View
    {
        return $this->viewService->auth('register');
    }

    #[Post(Routes::REGISTER, middleware: [GuestMiddleware::class])]
    public function store(Request $request): View | Response
    {
        try {
            // Create RegisterRequest for validation
            $registerRequest = new RegisterRequest(
                name: $request->body['name'] ?? '',
                email: $request->body['email'] ?? '',
                password: $request->body['password'] ?? '',
                password_confirmation: $request->body['password_confirmation'] ?? ''
            );

            // Manual validation for compatibility
            $errors = $this->validateRegisterRequest($registerRequest);
            if (!empty($errors)) {
                return $this->viewService->withErrors('auth/register', $errors, $request->body);
            }

            // Additional validation for password confirmation
            if (!$registerRequest->validatePasswordConfirmation()) {
                return $this->viewService->withErrors('auth/register', [
                    'password_confirmation' => 'Passwords do not match'
                ], $request->body);
            }

            // Check if user already exists
            if ($this->userRepository->emailExists($registerRequest->email)) {
                return $this->viewService->withError('auth/register', 'Email already registered', [
                    'old' => $request->body
                ]);
            }

            // Create user
            $userId = $this->userRepository->create($registerRequest->name, $registerRequest->email, $registerRequest->password);

            // Login user using centralized service
            $this->sessionService->login($userId, $registerRequest->email, $registerRequest->name);

            return $this->redirectWithSuccess(Routes::DASHBOARD, 'Registration successful! Welcome!');

        } catch (\Exception $e) {
            return $this->viewService->withError('auth/register', 'An error occurred during registration. Please try again.', [
                'old' => $request->body
            ]);
        }
    }

    /**
     * Validate register request manually for compatibility
     */
    private function validateRegisterRequest(RegisterRequest $registerRequest): array
    {
        $errors = [];

        if (empty($registerRequest->name)) {
            $errors['name'] = 'Name is required';
        }

        if (empty($registerRequest->email)) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($registerRequest->email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Email must be a valid email address';
        }

        if (empty($registerRequest->password)) {
            $errors['password'] = 'Password is required';
        } elseif (strlen($registerRequest->password) < 8) {
            $errors['password'] = 'Password must be at least 8 characters';
        }

        if (empty($registerRequest->password_confirmation)) {
            $errors['password_confirmation'] = 'Password confirmation is required';
        }

        return $errors;
    }




}
