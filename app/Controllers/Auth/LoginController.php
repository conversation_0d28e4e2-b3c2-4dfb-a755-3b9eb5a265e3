<?php

namespace App\Controllers\Auth;

use App\Controllers\BaseController;
use App\Controllers\Routes;
use App\Exceptions\AuthenticationException;
use App\Http\Requests\LoginRequest;
use App\Middleware\GuestMiddleware;
use App\Repositories\UserRepository;
use App\Services\SessionService;
use App\Services\ViewService;
use Tempest\Http\Request;
use Tempest\Http\Response;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\View\View;

/**
 * Improved LoginController using best practices
 * Uses dependency injection, repository pattern, and centralized services
 */
final readonly class LoginController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private UserRepository $userRepository,
        private ViewService $viewService
    ) {
        parent::__construct($sessionService);
    }

    #[Get(Routes::LOGIN, middleware: [GuestMiddleware::class])]
    public function show(): View
    {
        return $this->viewService->auth('login');
    }

    #[Post(Routes::LOGIN, middleware: [GuestMiddleware::class])]
    public function store(Request $request): View | Response
    {
        try {
            // Create LoginRequest for validation
            $loginRequest = new LoginRequest(
                email: $request->body['email'] ?? '',
                password: $request->body['password'] ?? ''
            );

            // Manual validation for compatibility
            $errors = $this->validateLoginRequest($loginRequest);
            if (!empty($errors)) {
                return $this->viewService->withErrors('auth/login', $errors, $request->body);
            }

            // Find user and verify credentials
            $user = $this->userRepository->findByEmail($loginRequest->email);
            if (!$user || !$this->userRepository->verifyPassword($user, $loginRequest->password)) {
                throw new AuthenticationException('Invalid credentials');
            }

            // Login user
            $this->sessionService->loginWithUser($user);

            return $this->redirectWithSuccess(Routes::DASHBOARD, 'Welcome back!');

        } catch (AuthenticationException $e) {
            return $this->viewService->withError('auth/login', $e->getMessage(), [
                'old' => $request->body
            ]);
        } catch (\Exception $e) {
            return $this->viewService->withError('auth/login', 'An error occurred. Please try again.', [
                'old' => $request->body
            ]);
        }
    }

    /**
     * Validate login request manually for compatibility
     */
    private function validateLoginRequest(LoginRequest $loginRequest): array
    {
        $errors = [];

        if (empty($loginRequest->email)) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($loginRequest->email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Email must be a valid email address';
        }

        if (empty($loginRequest->password)) {
            $errors['password'] = 'Password is required';
        }

        return $errors;
    }





    #[Post(Routes::LOGOUT)]
    public function destroy(): Response
    {
        // Logout user using centralized service
        $this->sessionService->logout();

        return $this->redirectWithSuccess(Routes::HOME, 'You have been logged out successfully');
    }
}
