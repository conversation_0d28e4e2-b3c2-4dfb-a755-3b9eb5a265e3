<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Csv;
use App\Http\Requests\CsvUploadRequest;
use App\Middleware\AuthMiddleware;
use App\Repositories\TimesheetRepository;
use App\Services\SessionService;
use App\Services\ViewService;
use League\Csv\Reader;
use League\Csv\Exception;
use Tempest\Http\Request;
use Tempest\Http\Response;
use Tempest\Router\Post;
use Tempest\View\View;
use DateTime;

/**
 * Improved ProcessCsvController using repository pattern and centralized services
 */
final readonly class ProcessCsvController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private TimesheetRepository $timesheetRepository,
        private ViewService $viewService
    ) {
        parent::__construct($sessionService);
    }

    #[Post('/process-csv', middleware: [AuthMiddleware::class])]
    public function __invoke(Request $request): View |Response
    {
        try {
            $userId = $this->getAuthenticatedUserId();

            // Create CsvUploadRequest for validation
            $csvRequest = new CsvUploadRequest(
                files: $request->files
            );

            // Validate CSV file
            $fileErrors = $csvRequest->validateCsvFile();
            if (!empty($fileErrors)) {
                return $this->viewService->dashboard([
                    'error' => reset($fileErrors),
                    'timesheets' => []
                ]);
            }

            // Validate CSV content and delimiter
            $contentErrors = $csvRequest->validateCsvContent();
            if (!empty($contentErrors)) {
                return $this->viewService->dashboard([
                    'error' => reset($contentErrors),
                    'timesheets' => []
                ]);
            }

            $file = $csvRequest->files['csvFile'];

            // Process CSV file
            $reader = Reader::createFromPath($file['tmp_name']);

            $reader->setDelimiter(';');
            $reader->setHeaderOffset(0);

            $records = $reader->getRecords();
            $timesheetData = [];

            // Prepare timesheet data for bulk creation
            foreach ($records as $record) {
                try {
                    // Create CSV object with Dutch headers
                    $csv = new Csv(
                        Boekjaar: (int) $record['Boekjaar'],
                        Week: (int) $record['Week'],
                        Datum: new DateTime($record['Datum']),
                        Persnr: (int) $record['Persnr'],
                        Uren: $record['Uren'],
                        Uurcode: $record['Uurcode']
                    );

                    if ($csv->validate()) {
                        $attributes = $csv->toModelAttributes();
                        $attributes['user_id'] = $userId;
                        $timesheetData[] = $attributes;
                    }
                } catch (Exception $e) {
                    // Skip invalid records
                    continue;
                }
            }

            // Use repository for bulk creation
            $processedCount = $this->timesheetRepository->bulkCreate($timesheetData);

            return $this->redirectWithSuccess('/dashboard', "Successfully imported {$processedCount} timesheet records");

        } catch (Exception $e) {
            return $this->viewService->dashboard([
                'error' => 'Error processing CSV file: ' . $e->getMessage(),
                'timesheets' => []
            ]);
        } catch (\Exception $e) {
            return $this->viewService->dashboard([
                'error' => 'An unexpected error occurred while processing the CSV file',
                'timesheets' => []
            ]);
        }
    }
}
