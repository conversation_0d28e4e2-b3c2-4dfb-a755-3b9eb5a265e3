<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Exceptions\AuthenticationException;
use App\Middleware\AuthMiddleware;
use App\Repositories\TimesheetRepository;
use App\Services\SessionService;
use League\Csv\Writer;
use Tempest\Http\Response;
use Tempest\Router\Get;

/**
 * DownloadTimesheetController using repository pattern and proper exception handling
 */
final readonly class DownloadTimesheetController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private TimesheetRepository $timesheetRepository
    ) {
        parent::__construct($sessionService);
    }

    #[Get('/export', middleware: [AuthMiddleware::class])]
    public function __invoke(): Response
    {
        try {
            $userId = $this->getAuthenticatedUserId();

            // Get all timesheets for the current user using repository
            $timesheets = $this->timesheetRepository->getAllForUser($userId);

            // Create CSV content
            $csv = Writer::createFromString();

            // Add headers (Dutch format to match import)
            $headers = [
                'Boekjaar',
                'Week',
                'Datum',
                'Persnr',
                'Uren',
                'Uurcode',
            ];

            $csv->insertOne($headers);

            // Add data rows
            foreach ($timesheets as $timesheet) {
                $row = [
                    $timesheet->booking_year,
                    $timesheet->week_number,
                    $timesheet->date->format('Y-m-d'),
                    $timesheet->employee_id,
                    $timesheet->hours_worked,
                    $timesheet->hours_worked_type,
                ];

                $csv->insertOne($row);
            }

            // Set headers for download
            $filename = 'timesheets_' . date('Y-m-d') . '.csv';

            return new class($csv->toString(), $filename) implements Response {
                public function __construct(
                    private string $content,
                    private string $filename
                ) {}

                public function send(): void
                {
                    header('Content-Type: text/csv; charset=utf-8');
                    header('Content-Disposition: attachment; filename="' . $this->filename . '"');
                    header('Content-Length: ' . strlen($this->content));

                    echo $this->content;
                }
            };

        } catch (AuthenticationException $e) {
            return $this->redirectWithError('/dashboard', 'Authentication required to export timesheets');
        } catch (\Exception $e) {
            return $this->redirectWithError('/dashboard', 'An error occurred while exporting timesheets');
        }
    }
}
