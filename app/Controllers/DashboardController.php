<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Exceptions\AuthenticationException;
use App\Middleware\AuthMiddleware;
use App\Repositories\TimesheetRepository;
use App\Services\SessionService;
use App\Services\ViewService;
use Tempest\Http\Request;
use Tempest\Router\Get;
use Tempest\View\View;

/**
 * Improved DashboardController using repository pattern and centralized services
 */
final readonly class DashboardController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private TimesheetRepository $timesheetRepository,
        private ViewService $viewService
    ) {
        parent::__construct($sessionService);
    }

    #[Get('/dashboard', middleware: [AuthMiddleware::class])]
    public function __invoke(Request $request): View
    {
        try {
            $userId = $this->getAuthenticatedUserId();

            // Get page number from query parameters
            $page = (int) ($request->get('page') ?? 1);
            $perPage = 25;

            // Get paginated timesheets using repository
            $paginationData = $this->timesheetRepository->getPaginatedForUser($userId, $page, $perPage);

            // Use ViewService for consistent dashboard rendering
            return $this->viewService->dashboard([
                'timesheets' => $paginationData['timesheets'],
                'currentPage' => $paginationData['currentPage'],
                'totalPages' => $paginationData['totalPages'],
                'hasNextPage' => $paginationData['hasNextPage'],
                'hasPrevPage' => $paginationData['hasPrevPage'],
                'totalCount' => $paginationData['totalCount'],
            ]);

        } catch (AuthenticationException $e) {
            return $this->redirectWithError('/login', 'Please log in to access your dashboard');
        } catch (\Exception $e) {
            // Fallback to empty dashboard with error message
            return $this->viewService->dashboard([
                'timesheets' => [],
                'currentPage' => 1,
                'totalPages' => 0,
                'hasNextPage' => false,
                'hasPrevPage' => false,
                'totalCount' => 0,
                'error' => 'An error occurred while loading your timesheets'
            ]);
        }
    }
}
