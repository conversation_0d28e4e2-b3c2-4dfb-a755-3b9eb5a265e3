<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Services\SessionService;
use App\Services\ViewService;
use Tempest\Router\Get;
use Tempest\View\View;

/**
 * HomeController using ViewService for consistent rendering
 */
final readonly class HomeController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private ViewService $viewService
    ) {
        parent::__construct($sessionService);
    }

    #[Get('/')]
    public function __invoke(): View
    {
        // Get user data using centralized service
        $user = $this->sessionService->getUser();

        return $this->viewService->welcome(['user' => $user]);
    }
}
