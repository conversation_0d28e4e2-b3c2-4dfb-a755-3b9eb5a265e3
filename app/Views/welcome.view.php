<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome - 365 Manage Hours</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 2rem auto;
            max-width: 600px;
            text-align: center;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.25rem;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #545b62;
        }
    </style>
</head>
<body>
    <main>
        <div class="container">
            <div class="card">
                <h1>Welcome to 365 Manage Hours</h1>

                <?php if ($user): ?>
                    <p>Hello, <?= htmlspecialchars($user['name']) ?>!</p>
                    <p>Manage your timesheet data by uploading CSV files and editing individual records.</p>
                    <a href="/dashboard" class="btn">Go to Dashboard</a>
                <?php else: ?>
                    <p>A simple timesheet management application built with Tempest PHP framework.</p>
                    <p>Upload CSV files, edit timesheet records, and export your data.</p>

                    <div style="display: flex; gap: 1rem; justify-content: center; margin-top: 2rem;">
                        <a href="/login" class="btn">Login</a>
                        <a href="/register" class="btn btn-secondary">Register</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</body>
</html>
