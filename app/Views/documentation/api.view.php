<x-layout>
    <div class="api-documentation">
        <div class="api-header">
            <h1>API Documentation</h1>
            <p class="api-subtitle">Complete API reference for 365 Manage Hours</p>
            <nav class="breadcrumb">
                <a href="/docs">← Back to Documentation</a>
            </nav>
        </div>

        <div class="api-overview">
            <h2>API Overview</h2>
            <p>
                The 365 Manage Hours application provides a RESTful API for managing timesheets, 
                user authentication, and CSV data processing. All endpoints follow standard HTTP 
                conventions and return appropriate status codes.
            </p>

            <div class="api-features">
                <div class="feature">
                    <h3>🔐 Authentication Required</h3>
                    <p>Most endpoints require user authentication via session middleware.</p>
                </div>
                <div class="feature">
                    <h3>✅ Request Validation</h3>
                    <p>All input is validated using Tempest's native Request classes.</p>
                </div>
                <div class="feature">
                    <h3>📊 Consistent Responses</h3>
                    <p>Standardized response formats for success and error cases.</p>
                </div>
            </div>
        </div>

        <?php foreach ($endpoints as $category => $group): ?>
        <section class="endpoint-section">
            <h2><?= htmlspecialchars($group['title']) ?></h2>
            
            <?php foreach ($group['endpoints'] as $endpoint): ?>
            <div class="endpoint">
                <div class="endpoint-header">
                    <span class="method method-<?= strtolower($endpoint['method']) ?>">
                        <?= htmlspecialchars($endpoint['method']) ?>
                    </span>
                    <code class="path"><?= htmlspecialchars($endpoint['path']) ?></code>
                </div>

                <div class="endpoint-body">
                    <p class="description"><?= htmlspecialchars($endpoint['description']) ?></p>

                    <?php if (isset($endpoint['middleware'])): ?>
                    <div class="middleware">
                        <h4>Middleware</h4>
                        <ul>
                            <?php foreach ($endpoint['middleware'] as $middleware): ?>
                            <li><code><?= htmlspecialchars($middleware) ?></code></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <div class="controller">
                        <h4>Controller</h4>
                        <code><?= htmlspecialchars($endpoint['controller']) ?></code>
                    </div>

                    <?php if (isset($endpoint['request'])): ?>
                    <div class="request-class">
                        <h4>Request Validation</h4>
                        <code><?= htmlspecialchars($endpoint['request']) ?></code>
                        <p class="request-note">
                            See <a href="/docs/validation">validation documentation</a> for detailed rules.
                        </p>
                    </div>
                    <?php endif; ?>

                    <?php if (isset($endpoint['parameters'])): ?>
                    <div class="parameters">
                        <h4>Parameters</h4>
                        <ul>
                            <?php foreach ($endpoint['parameters'] as $param => $description): ?>
                            <li>
                                <code><?= htmlspecialchars($param) ?></code>: 
                                <?= htmlspecialchars($description) ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endif; ?>

                    <div class="responses">
                        <h4>Responses</h4>
                        <?php if ($endpoint['method'] === 'GET'): ?>
                        <div class="response">
                            <span class="status-code success">200 OK</span>
                            <span class="response-desc">Returns the requested view/page</span>
                        </div>
                        <?php endif; ?>

                        <?php if ($endpoint['method'] === 'POST'): ?>
                        <div class="response">
                            <span class="status-code redirect">302 Found</span>
                            <span class="response-desc">Successful operation, redirects to appropriate page</span>
                        </div>
                        <div class="response">
                            <span class="status-code success">200 OK</span>
                            <span class="response-desc">Validation failed, returns form with errors</span>
                        </div>
                        <?php endif; ?>

                        <div class="response">
                            <span class="status-code error">401 Unauthorized</span>
                            <span class="response-desc">Authentication required (if protected)</span>
                        </div>
                    </div>

                    <?php if ($category === 'authentication'): ?>
                    <div class="example">
                        <h4>Example Request</h4>
                        <?php if ($endpoint['path'] === '/login' && $endpoint['method'] === 'POST'): ?>
                        <pre><code>POST /login
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>&password=secretpassword</code></pre>
                        <?php elseif ($endpoint['path'] === '/register' && $endpoint['method'] === 'POST'): ?>
                        <pre><code>POST /register
Content-Type: application/x-www-form-urlencoded

name=John+Doe&email=<EMAIL>&password=secretpassword&password_confirmation=secretpassword</code></pre>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>

                    <?php if ($category === 'timesheets' && $endpoint['method'] === 'POST'): ?>
                    <div class="example">
                        <h4>Example Request</h4>
                        <pre><code>POST /timesheets/123
Content-Type: application/x-www-form-urlencoded

booking_year=2024&week_number=30&date=2024-07-20&employee_id=320&hours_worked=8.5&hours_worked_type=REGULAR</code></pre>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </section>
        <?php endforeach; ?>

        <section class="error-handling">
            <h2>Error Handling</h2>
            <p>
                The application uses consistent error handling patterns across all endpoints.
            </p>

            <div class="error-types">
                <div class="error-type">
                    <h3>Validation Errors</h3>
                    <p>
                        When form validation fails, the application returns a 200 OK response 
                        with the form view containing error messages. Errors are displayed 
                        next to the relevant form fields.
                    </p>
                </div>

                <div class="error-type">
                    <h3>Authentication Errors</h3>
                    <p>
                        Unauthenticated requests to protected endpoints result in a redirect 
                        to the login page. Invalid credentials return the login form with 
                        an error message.
                    </p>
                </div>

                <div class="error-type">
                    <h3>Authorization Errors</h3>
                    <p>
                        Users can only access their own timesheet data. Attempts to access 
                        other users' data result in appropriate error responses.
                    </p>
                </div>

                <div class="error-type">
                    <h3>File Upload Errors</h3>
                    <p>
                        CSV upload errors are handled gracefully with detailed error messages 
                        indicating specific validation failures for each row.
                    </p>
                </div>
            </div>
        </section>

        <section class="request-examples">
            <h2>Complete Request Examples</h2>

            <div class="example-group">
                <h3>User Registration Flow</h3>
                <div class="example">
                    <h4>1. Get Registration Form</h4>
                    <pre><code>GET /register HTTP/1.1
Host: your-domain.com</code></pre>
                </div>

                <div class="example">
                    <h4>2. Submit Registration</h4>
                    <pre><code>POST /register HTTP/1.1
Host: your-domain.com
Content-Type: application/x-www-form-urlencoded

name=John+Doe&email=<EMAIL>&password=secretpassword&password_confirmation=secretpassword</code></pre>
                </div>

                <div class="example">
                    <h4>3. Success Response</h4>
                    <pre><code>HTTP/1.1 302 Found
Location: /dashboard</code></pre>
                </div>
            </div>

            <div class="example-group">
                <h3>Timesheet Update Flow</h3>
                <div class="example">
                    <h4>1. Get Edit Form</h4>
                    <pre><code>GET /timesheets/123/edit HTTP/1.1
Host: your-domain.com
Cookie: session_id=abc123...</code></pre>
                </div>

                <div class="example">
                    <h4>2. Submit Update</h4>
                    <pre><code>POST /timesheets/123 HTTP/1.1
Host: your-domain.com
Content-Type: application/x-www-form-urlencoded
Cookie: session_id=abc123...

booking_year=2024&week_number=30&date=2024-07-20&employee_id=320&hours_worked=8.5&hours_worked_type=REGULAR</code></pre>
                </div>

                <div class="example">
                    <h4>3. Success Response</h4>
                    <pre><code>HTTP/1.1 302 Found
Location: /dashboard</code></pre>
                </div>
            </div>

            <div class="example-group">
                <h3>CSV Upload Flow</h3>
                <div class="example">
                    <h4>1. Upload CSV File</h4>
                    <pre><code>POST /process-csv HTTP/1.1
Host: your-domain.com
Content-Type: multipart/form-data
Cookie: session_id=abc123...

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="csv_file"; filename="timesheets.csv"
Content-Type: text/csv

Boekjaar,Week,Datum,Persnr,Uren,Uurcode
2024,30,2024-07-20,320,8.5,REG
------WebKitFormBoundary7MA4YWxkTrZu0gW--</code></pre>
                </div>
            </div>
        </section>
    </div>

    <style>
        .api-documentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }

        .api-header {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .api-header h1 {
            font-size: 2.5rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .api-subtitle {
            font-size: 1.25rem;
            color: #718096;
            margin-bottom: 1rem;
        }

        .breadcrumb a {
            color: #4299e1;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .api-overview {
            margin-bottom: 3rem;
        }

        .api-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }

        .feature h3 {
            margin-top: 0;
            color: #2d3748;
        }

        .endpoint-section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .endpoint-section h2 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .endpoint {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 2rem;
            overflow: hidden;
        }

        .endpoint-header {
            background: #edf2f7;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .method {
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.875rem;
            text-transform: uppercase;
        }

        .method-get {
            background: #c6f6d5;
            color: #22543d;
        }

        .method-post {
            background: #fed7d7;
            color: #742a2a;
        }

        .path {
            font-family: 'Courier New', monospace;
            font-size: 1.1rem;
            color: #2d3748;
        }

        .endpoint-body {
            padding: 1.5rem;
        }

        .description {
            margin-bottom: 1.5rem;
            color: #4a5568;
        }

        .endpoint-body h4 {
            color: #2d3748;
            margin: 1.5rem 0 0.75rem 0;
            font-size: 1.1rem;
        }

        .endpoint-body ul {
            margin: 0.5rem 0;
            padding-left: 1.5rem;
        }

        .endpoint-body li {
            margin-bottom: 0.5rem;
        }

        .request-note {
            font-size: 0.9rem;
            color: #718096;
            margin-top: 0.5rem;
        }

        .request-note a {
            color: #4299e1;
            text-decoration: none;
        }

        .request-note a:hover {
            text-decoration: underline;
        }

        .responses {
            margin-top: 1.5rem;
        }

        .response {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
        }

        .status-code {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.875rem;
            font-family: 'Courier New', monospace;
        }

        .status-code.success {
            background: #c6f6d5;
            color: #22543d;
        }

        .status-code.redirect {
            background: #bee3f8;
            color: #2a4365;
        }

        .status-code.error {
            background: #fed7d7;
            color: #742a2a;
        }

        .response-desc {
            color: #4a5568;
        }

        .example {
            margin-top: 1.5rem;
        }

        .example h4 {
            margin-bottom: 0.5rem;
        }

        .example pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.9rem;
        }

        .example code {
            background: none;
            color: inherit;
            padding: 0;
        }

        .error-handling, .request-examples {
            margin-bottom: 3rem;
        }

        .error-handling h2, .request-examples h2 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 1.5rem;
        }

        .error-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .error-type {
            background: #fef5e7;
            border: 1px solid #f6e05e;
            padding: 1.5rem;
            border-radius: 8px;
        }

        .error-type h3 {
            margin-top: 0;
            color: #744210;
        }

        .example-group {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .example-group h3 {
            margin-top: 0;
            color: #2d3748;
        }

        code {
            background: #edf2f7;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .api-documentation {
                padding: 1rem;
            }

            .api-header h1 {
                font-size: 2rem;
            }

            .endpoint-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .api-features, .error-types {
                grid-template-columns: 1fr;
            }

            .response {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
        }
    </style>
</x-layout>
