<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? '365 Manage Hours - Documentation' ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 1rem;
        }

        nav a {
            color: #4b5563;
            text-decoration: none;
            font-weight: 500;
        }

        nav a:hover {
            color: #2563eb;
        }

        main {
            padding: 2rem 0;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="/docs">Documentation</a></li>
                        <li><a href="/docs/api">API</a></li>
                        <li><a href="/docs/validation">Validation</a></li>
                        <li><a href="/dashboard">Dashboard</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
        <div class="doc-header">
            <h1>365 Manage Hours</h1>
            <p class="doc-subtitle">Comprehensive Timesheet Management Application</p>
            <div class="doc-meta">
                <span class="version">Version <?= $version ?></span>
                <span class="updated">Last updated: <?= $lastUpdated ?></span>
            </div>
        </div>

        <nav class="doc-nav">
            <h2>Table of Contents</h2>
            <ul>
                <li><a href="#overview">Application Overview</a></li>
                <li><a href="#features">Core Features</a></li>
                <li><a href="#workflows">User Workflows</a></li>
                <li><a href="#architecture">Technical Architecture</a></li>
                <li><a href="#validation">Validation Rules</a></li>
                <li><a href="#api">API Reference</a></li>
                <li><a href="#deployment">Deployment</a></li>
            </ul>
        </nav>

        <section id="overview" class="doc-section">
            <h2>Application Overview</h2>
            <p>
                365 Manage Hours is a modern timesheet management application built with the
                <a href="https://tempestphp.com" target="_blank">Tempest PHP framework</a>.
                The application provides a comprehensive solution for tracking work hours,
                managing employee timesheets, and processing CSV data imports/exports.
            </p>

            <h3>Key Characteristics</h3>
            <ul>
                <li><strong>Framework:</strong> Built on Tempest PHP framework for modern PHP development</li>
                <li><strong>Architecture:</strong> Clean architecture with repository pattern and dependency injection</li>
                <li><strong>Validation:</strong> Comprehensive validation using Tempest's native Request classes</li>
                <li><strong>Security:</strong> Secure authentication and authorization with middleware protection</li>
                <li><strong>Data Processing:</strong> Robust CSV import/export functionality</li>
                <li><strong>Testing:</strong> Comprehensive test suite with 95+ tests covering all validation scenarios</li>
            </ul>

            <h3>Technology Stack</h3>
            <ul>
                <li><strong>Backend:</strong> PHP 8.4+ with Tempest Framework</li>
                <li><strong>Database:</strong> SQLite with Tempest ORM</li>
                <li><strong>CSV Processing:</strong> League CSV for robust file handling</li>
                <li><strong>Testing:</strong> PHPUnit with comprehensive test coverage</li>
                <li><strong>Frontend:</strong> Server-side rendered views with modern CSS</li>
            </ul>
        </section>

        <section id="features" class="doc-section">
            <h2>Core Features</h2>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔐 User Authentication</h3>
                    <ul>
                        <li>Secure user registration and login</li>
                        <li>Password hashing and verification</li>
                        <li>Session management</li>
                        <li>Middleware-based route protection</li>
                        <li>Guest and authenticated user handling</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>📊 Timesheet Management</h3>
                    <ul>
                        <li>Create and edit timesheet entries</li>
                        <li>Comprehensive validation rules</li>
                        <li>Business rule enforcement</li>
                        <li>Paginated timesheet listing</li>
                        <li>User-specific data isolation</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>📁 CSV Processing</h3>
                    <ul>
                        <li>Bulk CSV file upload</li>
                        <li>Automatic data validation</li>
                        <li>Error reporting and handling</li>
                        <li>CSV export functionality</li>
                        <li>Dutch format support</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3>✅ Data Validation</h3>
                    <ul>
                        <li>Tempest native Request validation</li>
                        <li>Business rule validation</li>
                        <li>Comprehensive error handling</li>
                        <li>Type-safe data processing</li>
                        <li>Edge case coverage</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="workflows" class="doc-section">
            <h2>User Workflows</h2>

            <div class="workflow">
                <h3>New User Registration</h3>
                <ol>
                    <li>User visits registration page</li>
                    <li>Fills out registration form (name, email, password)</li>
                    <li>System validates input and checks for existing email</li>
                    <li>User account is created and automatically logged in</li>
                    <li>User is redirected to dashboard</li>
                </ol>
            </div>

            <div class="workflow">
                <h3>Timesheet Entry Management</h3>
                <ol>
                    <li>User logs in and accesses dashboard</li>
                    <li>Views paginated list of existing timesheets</li>
                    <li>Clicks edit on a timesheet entry</li>
                    <li>Modifies timesheet data with validation feedback</li>
                    <li>Submits changes with comprehensive validation</li>
                    <li>Returns to dashboard with success confirmation</li>
                </ol>
            </div>

            <div class="workflow">
                <h3>CSV Data Import</h3>
                <ol>
                    <li>User prepares CSV file with required columns</li>
                    <li>Uploads file through the CSV processing interface</li>
                    <li>System validates file format and structure</li>
                    <li>Each row is validated against business rules</li>
                    <li>Valid entries are imported, errors are reported</li>
                    <li>User receives detailed import results</li>
                </ol>
            </div>

            <div class="workflow">
                <h3>Data Export</h3>
                <ol>
                    <li>User accesses export functionality</li>
                    <li>System generates CSV with all user timesheets</li>
                    <li>File is downloaded with proper headers</li>
                    <li>Data is formatted for external use</li>
                </ol>
            </div>
        </section>

        <section id="architecture" class="doc-section">
            <h2>Technical Architecture</h2>

            <h3>Framework Foundation</h3>
            <p>
                The application is built on the Tempest PHP framework, leveraging its modern
                features including dependency injection, attribute-based routing, and native
                validation system.
            </p>

            <h3>Architectural Patterns</h3>
            <div class="architecture-grid">
                <div class="pattern-card">
                    <h4>Repository Pattern</h4>
                    <p>Data access is abstracted through repository classes:</p>
                    <ul>
                        <li><code>UserRepository</code> - User management</li>
                        <li><code>TimesheetRepository</code> - Timesheet operations</li>
                    </ul>
                </div>

                <div class="pattern-card">
                    <h4>Request Validation</h4>
                    <p>Tempest native Request classes handle validation:</p>
                    <ul>
                        <li><code>LoginRequest</code> - Authentication validation</li>
                        <li><code>RegisterRequest</code> - Registration validation</li>
                        <li><code>TimesheetRequest</code> - Timesheet validation</li>
                        <li><code>CsvUploadRequest</code> - File upload validation</li>
                    </ul>
                </div>

                <div class="pattern-card">
                    <h4>Service Layer</h4>
                    <p>Business logic is encapsulated in service classes:</p>
                    <ul>
                        <li><code>SessionService</code> - Session management</li>
                        <li><code>ViewService</code> - View rendering</li>
                    </ul>
                </div>

                <div class="pattern-card">
                    <h4>Middleware</h4>
                    <p>Cross-cutting concerns handled by middleware:</p>
                    <ul>
                        <li><code>AuthMiddleware</code> - Authentication protection</li>
                        <li><code>GuestMiddleware</code> - Guest-only access</li>
                    </ul>
                </div>
            </div>

            <h3>Database Schema</h3>
            <div class="schema">
                <h4>Users Table</h4>
                <ul>
                    <li><code>id</code> - Primary key</li>
                    <li><code>name</code> - User full name</li>
                    <li><code>email</code> - Unique email address</li>
                    <li><code>password</code> - Hashed password</li>
                    <li><code>created_at</code> - Registration timestamp</li>
                    <li><code>updated_at</code> - Last update timestamp</li>
                </ul>

                <h4>Timesheets Table</h4>
                <ul>
                    <li><code>id</code> - Primary key</li>
                    <li><code>user_id</code> - Foreign key to users</li>
                    <li><code>booking_year</code> - Year for timesheet entry</li>
                    <li><code>week_number</code> - Week number (1-53)</li>
                    <li><code>date</code> - Entry date</li>
                    <li><code>employee_id</code> - Employee identifier</li>
                    <li><code>hours_worked</code> - Hours worked (0-24)</li>
                    <li><code>hours_worked_type</code> - Type of hours</li>
                    <li><code>created_at</code> - Creation timestamp</li>
                    <li><code>updated_at</code> - Last update timestamp</li>
                </ul>
            </div>
        </section>

        <section id="validation" class="doc-section">
            <h2>Validation Rules</h2>
            <p>
                The application implements comprehensive validation at multiple levels to ensure
                data integrity and business rule compliance.
            </p>

            <div class="validation-info">
                <p>
                    <strong>📖 Detailed validation documentation:</strong>
                    <a href="/docs/validation">View complete validation rules →</a>
                </p>
            </div>

            <h3>Timesheet Business Rules</h3>
            <div class="rules-grid">
                <div class="rule-card">
                    <h4>Week Number Validation</h4>
                    <ul>
                        <li><strong>Range:</strong> 1-53 (ISO week numbering)</li>
                        <li><strong>Type:</strong> Numeric string</li>
                        <li><strong>Required:</strong> Yes</li>
                        <li><strong>Note:</strong> Week 0 is invalid and properly rejected</li>
                    </ul>
                </div>

                <div class="rule-card">
                    <h4>Hours Worked Validation</h4>
                    <ul>
                        <li><strong>Range:</strong> 0-24 hours</li>
                        <li><strong>Type:</strong> Numeric (decimal allowed)</li>
                        <li><strong>Required:</strong> Yes</li>
                        <li><strong>Examples:</strong> 0, 8.5, 24.0</li>
                    </ul>
                </div>

                <div class="rule-card">
                    <h4>Date Validation</h4>
                    <ul>
                        <li><strong>Format:</strong> Y-m-d (2024-01-15)</li>
                        <li><strong>Validation:</strong> Must be parseable by strtotime()</li>
                        <li><strong>Required:</strong> Yes</li>
                        <li><strong>Examples:</strong> Valid dates, leap year handling</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="api" class="doc-section">
            <h2>API Reference</h2>
            <p>
                Complete API endpoint documentation with request/response details.
            </p>

            <div class="api-info">
                <p>
                    <strong>📖 Complete API documentation:</strong>
                    <a href="/docs/api">View API reference →</a>
                </p>
            </div>

            <h3>Quick Reference</h3>
            <div class="endpoint-summary">
                <div class="endpoint-group">
                    <h4>Authentication</h4>
                    <ul>
                        <li><code>GET /login</code> - Login form</li>
                        <li><code>POST /login</code> - Authenticate</li>
                        <li><code>GET /register</code> - Registration form</li>
                        <li><code>POST /register</code> - Register user</li>
                        <li><code>POST /logout</code> - Logout</li>
                    </ul>
                </div>

                <div class="endpoint-group">
                    <h4>Timesheets</h4>
                    <ul>
                        <li><code>GET /dashboard</code> - Timesheet listing</li>
                        <li><code>GET /timesheets/{id}/edit</code> - Edit form</li>
                        <li><code>POST /timesheets/{id}</code> - Update timesheet</li>
                    </ul>
                </div>

                <div class="endpoint-group">
                    <h4>CSV Processing</h4>
                    <ul>
                        <li><code>POST /process-csv</code> - Upload CSV</li>
                        <li><code>GET /export</code> - Export CSV</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="deployment" class="doc-section">
            <h2>Deployment</h2>

            <h3>Requirements</h3>
            <ul>
                <li>PHP 8.4 or higher</li>
                <li>Composer for dependency management</li>
                <li>SQLite support (or other database)</li>
                <li>Web server (Apache, Nginx, or PHP built-in server)</li>
            </ul>

            <h3>Installation Steps</h3>
            <ol>
                <li>Clone the repository</li>
                <li>Run <code>composer install</code></li>
                <li>Set up database with <code>./tempest migrate</code></li>
                <li>Configure web server to point to <code>/public</code> directory</li>
                <li>Generate static documentation: <code>./tempest static:generate</code></li>
            </ol>

            <h3>Testing</h3>
            <p>Run the comprehensive test suite:</p>
            <pre><code>composer test</code></pre>
            <p>The application includes 95+ tests covering all validation scenarios and business logic.</p>
        </section>
            </div>
        </div>
    </main>

    <style>
        .documentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }

        .doc-header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .doc-header h1 {
            font-size: 3rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .doc-subtitle {
            font-size: 1.25rem;
            color: #718096;
            margin-bottom: 1rem;
        }

        .doc-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            font-size: 0.9rem;
            color: #a0aec0;
        }

        .doc-nav {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .doc-nav h2 {
            margin-top: 0;
            color: #2d3748;
        }

        .doc-nav ul {
            list-style: none;
            padding: 0;
        }

        .doc-nav li {
            margin-bottom: 0.5rem;
        }

        .doc-nav a {
            color: #4299e1;
            text-decoration: none;
            font-weight: 500;
        }

        .doc-nav a:hover {
            text-decoration: underline;
        }

        .doc-section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .doc-section:last-child {
            border-bottom: none;
        }

        .doc-section h2 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h3 {
            color: #4a5568;
            font-size: 1.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h4 {
            color: #718096;
            font-size: 1.25rem;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .feature-grid, .architecture-grid, .rules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .feature-card, .pattern-card, .rule-card {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }

        .feature-card h3, .pattern-card h4, .rule-card h4 {
            margin-top: 0;
            color: #2d3748;
        }

        .workflow {
            background: #edf2f7;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .workflow h3 {
            margin-top: 0;
            color: #2d3748;
        }

        .workflow ol {
            margin: 0;
            padding-left: 1.5rem;
        }

        .workflow li {
            margin-bottom: 0.5rem;
        }

        .schema {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .schema h4 {
            color: #2d3748;
            margin-bottom: 0.75rem;
        }

        .schema ul {
            margin: 0 0 1.5rem 0;
            padding-left: 1.5rem;
        }

        .schema li {
            margin-bottom: 0.25rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .validation-info, .api-info {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .validation-info a, .api-info a {
            color: #2c7a7b;
            font-weight: 600;
            text-decoration: none;
        }

        .validation-info a:hover, .api-info a:hover {
            text-decoration: underline;
        }

        .endpoint-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .endpoint-group {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
        }

        .endpoint-group h4 {
            margin-top: 0;
            color: #2d3748;
        }

        .endpoint-group ul {
            margin: 0;
            padding-left: 1rem;
        }

        .endpoint-group li {
            margin-bottom: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        code {
            background: #edf2f7;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
        }

        pre code {
            background: none;
            padding: 0;
            color: inherit;
        }

        @media (max-width: 768px) {
            .documentation {
                padding: 1rem;
            }

            .doc-header h1 {
                font-size: 2rem;
            }

            .doc-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .feature-grid, .architecture-grid, .rules-grid, .endpoint-summary {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
