<x-layout>
    <div class="validation-documentation">
        <div class="validation-header">
            <h1>Validation Rules</h1>
            <p class="validation-subtitle">Complete validation rules and business logic documentation</p>
            <nav class="breadcrumb">
                <a href="/docs">← Back to Documentation</a>
            </nav>
        </div>

        <div class="validation-overview">
            <h2>Validation Overview</h2>
            <p>
                The 365 Manage Hours application implements comprehensive validation at multiple 
                levels to ensure data integrity and business rule compliance. The validation 
                system uses Tempest's native Request classes with PHP attributes for type-safe 
                validation.
            </p>

            <div class="validation-layers">
                <div class="layer">
                    <h3>🏷️ Tempest Attributes</h3>
                    <p>PHP attributes like #[Numeric], #[Email], and #[Length] provide basic type validation.</p>
                </div>
                <div class="layer">
                    <h3>🔧 Controller Validation</h3>
                    <p>Required field validation and basic data integrity checks in controllers.</p>
                </div>
                <div class="layer">
                    <h3>📋 Business Rules</h3>
                    <p>Complex business logic validation in Request class methods.</p>
                </div>
            </div>
        </div>

        <?php foreach ($validationRules as $category => $rules): ?>
        <section class="validation-section">
            <h2><?= htmlspecialchars($rules['title']) ?></h2>
            <p class="section-description"><?= htmlspecialchars($rules['description']) ?></p>

            <?php if ($category === 'timesheet'): ?>
            <div class="timesheet-validation">
                <h3>Field Validation Rules</h3>
                <div class="fields-grid">
                    <?php foreach ($rules['fields'] as $fieldName => $field): ?>
                    <div class="field-card">
                        <h4><?= htmlspecialchars($fieldName) ?></h4>
                        <div class="field-details">
                            <div class="field-meta">
                                <span class="field-type"><?= htmlspecialchars($field['type']) ?></span>
                                <?php if ($field['required']): ?>
                                <span class="required-badge">Required</span>
                                <?php endif; ?>
                            </div>
                            <p class="field-description"><?= htmlspecialchars($field['description']) ?></p>
                            
                            <div class="validation-rules">
                                <h5>Validation Rules</h5>
                                <ul>
                                    <?php foreach ($field['validation'] as $rule): ?>
                                    <li><?= htmlspecialchars($rule) ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>

                            <?php if (isset($field['business_rules'])): ?>
                            <div class="business-rules">
                                <h5>Business Rules</h5>
                                <p><?= htmlspecialchars($field['business_rules']) ?></p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="validation-examples">
                    <h3>Validation Examples</h3>
                    
                    <div class="example-group">
                        <h4>Week Number Validation</h4>
                        <div class="examples">
                            <div class="example valid">
                                <h5>✅ Valid Examples</h5>
                                <ul>
                                    <li><code>'1'</code> - Minimum valid week</li>
                                    <li><code>'25'</code> - Mid-year week</li>
                                    <li><code>'53'</code> - Maximum valid week</li>
                                </ul>
                            </div>
                            <div class="example invalid">
                                <h5>❌ Invalid Examples</h5>
                                <ul>
                                    <li><code>'0'</code> - Below minimum (now properly rejected)</li>
                                    <li><code>'-1'</code> - Negative number</li>
                                    <li><code>'54'</code> - Above maximum</li>
                                    <li><code>'abc'</code> - Non-numeric</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="example-group">
                        <h4>Hours Worked Validation</h4>
                        <div class="examples">
                            <div class="example valid">
                                <h5>✅ Valid Examples</h5>
                                <ul>
                                    <li><code>'0'</code> - Minimum valid (no hours)</li>
                                    <li><code>'8.5'</code> - Standard workday with overtime</li>
                                    <li><code>'24'</code> - Maximum valid hours</li>
                                    <li><code>'0.25'</code> - Quarter hour</li>
                                </ul>
                            </div>
                            <div class="example invalid">
                                <h5>❌ Invalid Examples</h5>
                                <ul>
                                    <li><code>'-1'</code> - Negative hours</li>
                                    <li><code>'24.1'</code> - Above maximum</li>
                                    <li><code>'48'</code> - Excessive hours</li>
                                    <li><code>'eight'</code> - Non-numeric</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="example-group">
                        <h4>Date Format Validation</h4>
                        <div class="examples">
                            <div class="example valid">
                                <h5>✅ Valid Examples</h5>
                                <ul>
                                    <li><code>'2024-01-15'</code> - Standard format</li>
                                    <li><code>'2024-02-29'</code> - Leap year date</li>
                                    <li><code>'2024-12-31'</code> - End of year</li>
                                </ul>
                            </div>
                            <div class="example invalid">
                                <h5>❌ Invalid Examples</h5>
                                <ul>
                                    <li><code>'invalid-date'</code> - Not a date</li>
                                    <li><code>'2024-13-45'</code> - Invalid month/day</li>
                                    <li><code>'2023-02-29'</code> - Non-leap year Feb 29</li>
                                    <li><code>'2024/01/15'</code> - Wrong separator</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($category === 'authentication'): ?>
            <div class="auth-validation">
                <h3>Login Validation</h3>
                <div class="auth-fields">
                    <?php foreach ($rules['login'] as $fieldName => $field): ?>
                    <div class="auth-field">
                        <h4><?= htmlspecialchars($fieldName) ?></h4>
                        <div class="field-info">
                            <span class="field-type"><?= htmlspecialchars($field['type']) ?></span>
                            <?php if ($field['required']): ?>
                            <span class="required-badge">Required</span>
                            <?php endif; ?>
                        </div>
                        <p><?= htmlspecialchars($field['description']) ?></p>
                        <ul class="validation-list">
                            <?php foreach ($field['validation'] as $rule): ?>
                            <li><?= htmlspecialchars($rule) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endforeach; ?>
                </div>

                <h3>Registration Validation</h3>
                <div class="auth-fields">
                    <?php foreach ($rules['registration'] as $fieldName => $field): ?>
                    <div class="auth-field">
                        <h4><?= htmlspecialchars($fieldName) ?></h4>
                        <div class="field-info">
                            <span class="field-type"><?= htmlspecialchars($field['type']) ?></span>
                            <?php if ($field['required']): ?>
                            <span class="required-badge">Required</span>
                            <?php endif; ?>
                        </div>
                        <p><?= htmlspecialchars($field['description']) ?></p>
                        <ul class="validation-list">
                            <?php foreach ($field['validation'] as $rule): ?>
                            <li><?= htmlspecialchars($rule) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($category === 'csv_upload'): ?>
            <div class="csv-validation">
                <h3>File Validation</h3>
                <div class="file-rules">
                    <?php foreach ($rules['file_validation'] as $rule => $description): ?>
                    <div class="file-rule">
                        <h4><?= htmlspecialchars(ucwords(str_replace('_', ' ', $rule))) ?></h4>
                        <p><?= htmlspecialchars($description) ?></p>
                    </div>
                    <?php endforeach; ?>
                </div>

                <h3>Required CSV Columns</h3>
                <div class="csv-columns">
                    <table class="columns-table">
                        <thead>
                            <tr>
                                <th>Column Name</th>
                                <th>Description</th>
                                <th>Validation</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($rules['required_columns'] as $column => $description): ?>
                            <tr>
                                <td><code><?= htmlspecialchars($column) ?></code></td>
                                <td><?= htmlspecialchars($description) ?></td>
                                <td>
                                    <?php if ($column === 'Boekjaar'): ?>
                                    Numeric, 4-digit year
                                    <?php elseif ($column === 'Week'): ?>
                                    Numeric, 1-53
                                    <?php elseif ($column === 'Datum'): ?>
                                    Valid date format
                                    <?php elseif ($column === 'Persnr'): ?>
                                    Numeric employee ID
                                    <?php elseif ($column === 'Uren'): ?>
                                    Numeric, 0-24
                                    <?php else: ?>
                                    Required string
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>
        </section>
        <?php endforeach; ?>

        <section class="validation-implementation">
            <h2>Implementation Details</h2>

            <div class="implementation-info">
                <h3>Request Class Structure</h3>
                <p>
                    All validation is implemented using Tempest's native Request classes with 
                    PHP attributes for type validation and custom methods for business rules.
                </p>

                <div class="code-example">
                    <h4>Example: TimesheetRequest Class</h4>
                    <pre><code>final readonly class TimesheetRequest
{
    public function __construct(
        #[Numeric] public string $booking_year,
        #[Numeric] public string $week_number,
        public string $date,
        #[Numeric] public string $employee_id,
        #[Numeric] public string $hours_worked,
        public string $hours_worked_type,
    ) {}

    public function validateBusinessRules(): array
    {
        $errors = [];

        // Date validation
        if ($this->date !== '' && strtotime($this->date) === false) {
            $errors['date'] = 'Date must be a valid date';
        }

        // Week number range validation
        if ($this->week_number !== '' && is_numeric($this->week_number)) {
            $weekNum = (int) $this->week_number;
            if ($weekNum < 1 || $weekNum > 53) {
                $errors['week_number'] = 'Week number must be between 1 and 53';
            }
        }

        // Hours worked range validation
        if ($this->hours_worked !== '' && is_numeric($this->hours_worked)) {
            $hours = (float) $this->hours_worked;
            if ($hours < 0 || $hours > 24) {
                $errors['hours_worked'] = 'Hours worked must be between 0 and 24';
            }
        }

        return $errors;
    }
}</code></pre>
                </div>
            </div>

            <div class="bug-fixes">
                <h3>Recent Bug Fixes</h3>
                <div class="bug-fix">
                    <h4>🐛 Fixed: empty('0') Validation Bug</h4>
                    <p>
                        <strong>Issue:</strong> Week number '0' was incorrectly skipped due to 
                        PHP's <code>empty('0')</code> returning <code>true</code>.
                    </p>
                    <p>
                        <strong>Solution:</strong> Replaced <code>!empty()</code> checks with 
                        <code>!== ''</code> for proper string comparison.
                    </p>
                    <p>
                        <strong>Impact:</strong> Week number '0' now correctly fails validation 
                        while hours worked '0' correctly passes validation.
                    </p>
                </div>
            </div>

            <div class="test-coverage">
                <h3>Test Coverage</h3>
                <p>
                    The validation system is backed by comprehensive test coverage:
                </p>
                <ul>
                    <li><strong>48 unit tests</strong> for TimesheetRequest validateBusinessRules() method</li>
                    <li><strong>40 integration tests</strong> for complete validation flow</li>
                    <li><strong>7 enhanced controller tests</strong> with boundary testing</li>
                    <li><strong>Total: 95 tests, 238 assertions</strong> - 100% pass rate</li>
                </ul>
            </div>
        </section>
    </div>

    <style>
        .validation-documentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
        }

        .validation-header {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .validation-header h1 {
            font-size: 2.5rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .validation-subtitle {
            font-size: 1.25rem;
            color: #718096;
            margin-bottom: 1rem;
        }

        .breadcrumb a {
            color: #4299e1;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .validation-overview {
            margin-bottom: 3rem;
        }

        .validation-layers {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .layer {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }

        .layer h3 {
            margin-top: 0;
            color: #2d3748;
        }

        .validation-section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .validation-section h2 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .section-description {
            color: #4a5568;
            margin-bottom: 2rem;
        }

        .fields-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .field-card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
        }

        .field-card h4 {
            margin-top: 0;
            color: #2d3748;
            font-family: 'Courier New', monospace;
        }

        .field-meta {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .field-type {
            background: #edf2f7;
            color: #4a5568;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-family: 'Courier New', monospace;
        }

        .required-badge {
            background: #fed7d7;
            color: #742a2a;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: bold;
        }

        .field-description {
            color: #4a5568;
            margin-bottom: 1rem;
        }

        .validation-rules h5, .business-rules h5 {
            color: #2d3748;
            margin: 1rem 0 0.5rem 0;
            font-size: 1rem;
        }

        .validation-rules ul, .validation-list {
            margin: 0;
            padding-left: 1.5rem;
        }

        .validation-rules li, .validation-list li {
            margin-bottom: 0.25rem;
            color: #4a5568;
        }

        .business-rules p {
            color: #4a5568;
            font-style: italic;
            margin: 0;
        }

        .validation-examples {
            margin-top: 3rem;
        }

        .validation-examples h3 {
            color: #2d3748;
            margin-bottom: 2rem;
        }

        .example-group {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .example-group h4 {
            margin-top: 0;
            color: #2d3748;
        }

        .examples {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .example {
            padding: 1rem;
            border-radius: 6px;
        }

        .example.valid {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
        }

        .example.invalid {
            background: #fff5f5;
            border: 1px solid #feb2b2;
        }

        .example h5 {
            margin-top: 0;
            margin-bottom: 0.75rem;
        }

        .example.valid h5 {
            color: #22543d;
        }

        .example.invalid h5 {
            color: #742a2a;
        }

        .example ul {
            margin: 0;
            padding-left: 1.5rem;
        }

        .example li {
            margin-bottom: 0.5rem;
        }

        .auth-validation, .csv-validation {
            margin-top: 2rem;
        }

        .auth-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .auth-field {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }

        .auth-field h4 {
            margin-top: 0;
            color: #2d3748;
            font-family: 'Courier New', monospace;
        }

        .field-info {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .file-rules {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .file-rule {
            background: #fef5e7;
            border: 1px solid #f6e05e;
            padding: 1.5rem;
            border-radius: 8px;
        }

        .file-rule h4 {
            margin-top: 0;
            color: #744210;
        }

        .columns-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .columns-table th {
            background: #edf2f7;
            color: #2d3748;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .columns-table td {
            padding: 1rem;
            border-top: 1px solid #e2e8f0;
            color: #4a5568;
        }

        .columns-table tr:hover {
            background: #f7fafc;
        }

        .validation-implementation {
            margin-top: 3rem;
        }

        .implementation-info, .bug-fixes, .test-coverage {
            margin-bottom: 2rem;
        }

        .code-example {
            margin: 1.5rem 0;
        }

        .code-example h4 {
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .code-example pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 0.9rem;
        }

        .code-example code {
            background: none;
            color: inherit;
            padding: 0;
        }

        .bug-fix {
            background: #e6fffa;
            border: 1px solid #81e6d9;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 1rem 0;
        }

        .bug-fix h4 {
            margin-top: 0;
            color: #2c7a7b;
        }

        .bug-fix p {
            margin-bottom: 0.75rem;
            color: #4a5568;
        }

        .bug-fix strong {
            color: #2d3748;
        }

        code {
            background: #edf2f7;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .validation-documentation {
                padding: 1rem;
            }

            .validation-header h1 {
                font-size: 2rem;
            }

            .validation-layers, .fields-grid, .auth-fields, .file-rules {
                grid-template-columns: 1fr;
            }

            .examples {
                grid-template-columns: 1fr;
            }

            .columns-table {
                font-size: 0.875rem;
            }

            .columns-table th, .columns-table td {
                padding: 0.75rem;
            }
        }
    </style>
</x-layout>
