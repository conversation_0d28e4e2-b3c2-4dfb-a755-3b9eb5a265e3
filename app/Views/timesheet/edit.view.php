<x-layout title="Edit Timesheet">
    <div class="card" style="max-width: 600px; margin: 2rem auto;">
        <h1>Edit Timesheet</h1>
        
        <?php if (isset($errors) && !empty($errors)): ?>
            <div class="alert alert-error">
                <ul style="margin: 0; padding-left: 1rem;">
                    <?php foreach ($errors as $error): ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="/timesheets/<?= $timesheet->id ?>">
            <div class="form-group">
                <label for="booking_year">Booking Year</label>
                <input 
                    type="number" 
                    id="booking_year" 
                    name="booking_year" 
                    value="<?= htmlspecialchars($old['booking_year'] ?? $timesheet->booking_year) ?>"
                    required
                >
                <?php if (isset($errors['booking_year'])): ?>
                    <div class="error"><?= htmlspecialchars($errors['booking_year']) ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="week_number">Week Number</label>
                <input 
                    type="number" 
                    id="week_number" 
                    name="week_number" 
                    value="<?= htmlspecialchars($old['week_number'] ?? $timesheet->week_number) ?>"
                    min="1" 
                    max="53"
                    required
                >
                <?php if (isset($errors['week_number'])): ?>
                    <div class="error"><?= htmlspecialchars($errors['week_number']) ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="date">Date</label>
                <input 
                    type="date" 
                    id="date" 
                    name="date" 
                    value="<?= htmlspecialchars($old['date'] ?? $timesheet->date->format('Y-m-d')) ?>"
                    required
                >
                <?php if (isset($errors['date'])): ?>
                    <div class="error"><?= htmlspecialchars($errors['date']) ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="employee_id">Employee ID</label>
                <input 
                    type="number" 
                    id="employee_id" 
                    name="employee_id" 
                    value="<?= htmlspecialchars($old['employee_id'] ?? $timesheet->employee_id) ?>"
                    required
                >
                <?php if (isset($errors['employee_id'])): ?>
                    <div class="error"><?= htmlspecialchars($errors['employee_id']) ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="hours_worked">Hours Worked</label>
                <input 
                    type="number" 
                    id="hours_worked" 
                    name="hours_worked" 
                    value="<?= htmlspecialchars($old['hours_worked'] ?? $timesheet->hours_worked) ?>"
                    step="0.1"
                    min="0"
                    required
                >
                <?php if (isset($errors['hours_worked'])): ?>
                    <div class="error"><?= htmlspecialchars($errors['hours_worked']) ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="hours_worked_type">Hours Worked Type</label>
                <input 
                    type="text" 
                    id="hours_worked_type" 
                    name="hours_worked_type" 
                    value="<?= htmlspecialchars($old['hours_worked_type'] ?? $timesheet->hours_worked_type) ?>"
                    required
                >
                <?php if (isset($errors['hours_worked_type'])): ?>
                    <div class="error"><?= htmlspecialchars($errors['hours_worked_type']) ?></div>
                <?php endif; ?>
            </div>
            
            <div class="form-group" style="display: flex; gap: 1rem;">
                <button type="submit" class="btn">Save Changes</button>
                <a href="/dashboard" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</x-layout>
