<x-layout title="Dashboard">
    <h1>Dashboard</h1>

    <?php if (isset($error)): ?>
        <div class="alert alert-error">
            <?= htmlspecialchars($error) ?>
        </div>
    <?php endif; ?>

    <?php if (isset($success)): ?>
        <div class="alert alert-success">
            <?= htmlspecialchars($success) ?>
        </div>
    <?php endif; ?>

    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; margin-bottom: 2rem;">
        <div class="card">
            <h2>Upload CSV File</h2>
            <p>Upload a semicolon-delimited CSV file with timesheet data.</p>
            <form method="POST" action="/process-csv" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="csvFile">CSV File</label>
                    <input type="file" id="csvFile" name="csvFile" accept=".csv,.txt" required>
                </div>
                <button type="submit" class="btn">Upload</button>
            </form>
        </div>

        <div class="card">
            <h2>Export Data</h2>
            <p>Download your timesheet data as a CSV file.</p>
            <a href="/export" class="btn">Download CSV</a>
        </div>
    </div>

    <div class="card">
        <h2>Your Timesheets</h2>

        <?php if (empty($timesheets)): ?>
            <p>No timesheets found. Upload a CSV file to get started.</p>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>Booking Year</th>
                        <th>Week Number</th>
                        <th>Date</th>
                        <th>Employee ID</th>
                        <th>Hours Worked</th>
                        <th>Hours Type</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($timesheets as $timesheet): ?>
                        <tr>
                            <td><?= htmlspecialchars($timesheet->booking_year) ?></td>
                            <td><?= htmlspecialchars($timesheet->week_number) ?></td>
                            <td><?= htmlspecialchars($timesheet->date->format('Y-m-d')) ?></td>
                            <td><?= htmlspecialchars($timesheet->employee_id) ?></td>
                            <td><?= htmlspecialchars($timesheet->hours_worked) ?></td>
                            <td><?= htmlspecialchars($timesheet->hours_worked_type) ?></td>
                            <td>
                                <a href="/timesheets/<?= $timesheet->id ?>/edit" class="btn btn-secondary" style="padding: 0.5rem 1rem; font-size: 0.875rem;">Edit</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <?php if (isset($totalPages) && $totalPages > 1): ?>
                <div class="pagination">
                    <?php if ($hasPrevPage): ?>
                        <a href="/dashboard?page=<?= $currentPage - 1 ?>">Previous</a>
                    <?php endif; ?>

                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                        <?php if ($i == $currentPage): ?>
                            <span class="current"><?= $i ?></span>
                        <?php else: ?>
                            <a href="/dashboard?page=<?= $i ?>"><?= $i ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <?php if ($hasNextPage): ?>
                        <a href="/dashboard?page=<?= $currentPage + 1 ?>">Next</a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</x-layout>
