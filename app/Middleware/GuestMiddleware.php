<?php

namespace App\Middleware;

use App\Controllers\Routes;
use App\Services\SessionService;
use Tempest\Discovery\SkipDiscovery;
use Tempest\Router\HttpMiddleware;
use Tempest\Router\HttpMiddlewareCallable;
use Tempest\Http\Request;
use Tempest\Http\Response;
use Tempest\Http\Responses\Redirect;

#[SkipDiscovery]
final readonly class GuestMiddleware implements HttpMiddleware
{
    public function __construct(private SessionService $sessionService)
    {
        //
    }

    public function __invoke(Request $request, HttpMiddlewareCallable $next): Response
    {
        // If user is already authenticated, redirect to dashboard
        if ($this->sessionService->isAuthenticated()) {
            return new Redirect(Routes::DASHBOARD);
        }

        return $next($request);
    }
}
