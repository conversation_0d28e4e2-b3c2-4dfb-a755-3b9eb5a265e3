<?php

namespace App\Middleware;

use App\Controllers\Routes;
use App\Services\SessionService;
use Tempest\Discovery\SkipDiscovery;
use Tempest\Router\HttpMiddleware;
use Tempest\Router\HttpMiddlewareCallable;
use Tempest\Http\Request;
use Tempest\Http\Response;
use Tempest\Http\Responses\Redirect;

#[SkipDiscovery]
final readonly class AuthMiddleware implements HttpMiddleware
{
    public function __construct(private SessionService $sessionService)
    {
        //
    }

    public function __invoke(Request $request, HttpMiddlewareCallable $next): Response
    {
        // Check if user is authenticated using centralized service
        if (!$this->sessionService->isAuthenticated()) {
            return new Redirect(Routes::LOGIN);
        }

        return $next($request);
    }
}
