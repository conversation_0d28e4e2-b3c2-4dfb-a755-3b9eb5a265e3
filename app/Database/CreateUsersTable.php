<?php

namespace App\Database;

use Tempest\Database\DatabaseMigration;
use Tempest\Database\QueryStatement;
use Tempest\Database\QueryStatements\CreateTableStatement;
use Tempest\Database\QueryStatements\DropTableStatement;

final class CreateUsersTable implements DatabaseMigration
{
    public string $name = '2025-01-01_create_users_table';

    public function up(): QueryStatement|null
    {
        return new CreateTableStatement('users')
            ->primary()
            ->text('name')
            ->text('email')
            ->datetime('email_verified_at', nullable: true)
            ->text('password')
            ->text('remember_token', nullable: true)
            ->datetime('created_at')
            ->datetime('updated_at')
            ->unique('email');
    }

    public function down(): QueryStatement|null
    {
        return new DropTableStatement('users');
    }
}
