<?php

namespace App\Database;

use Tempest\Database\DatabaseMigration;
use Tempest\Database\QueryStatement;
use Tempest\Database\QueryStatements\CreateTableStatement;
use Tempest\Database\QueryStatements\DropTableStatement;

final class CreateTimesheetsTable implements DatabaseMigration
{
    public string $name = '2025-01-02_create_timesheets_table';

    public function up(): QueryStatement|null
    {
        return new CreateTableStatement('timesheets')
            ->primary()
            ->belongsTo('timesheets.user_id', 'users.id')
            ->integer('booking_year')
            ->integer('week_number')
            ->date('date')
            ->integer('employee_id')
            ->float('hours_worked')
            ->text('hours_worked_type')
            ->datetime('created_at')
            ->datetime('updated_at');
    }

    public function down(): QueryStatement|null
    {
        return new DropTableStatement('timesheets');
    }
}
