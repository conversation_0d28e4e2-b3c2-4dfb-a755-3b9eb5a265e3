<?php

namespace App\Database;

use App\Models\User;
use App\Models\Timesheet;
use Tempest\Database\DatabaseSeeder;
use DateTime;
use UnitEnum;

use function Tempest\Database\query;

final class TestDataSeeder implements DatabaseSeeder
{
    public function run(null|string|UnitEnum $database): void
    {
        // Create a test user
        $userId = query(User::class)
            ->insert(
                name: 'Test User',
                email: '<EMAIL>',
                password: password_hash('password', PASSWORD_DEFAULT),
                created_at: new DateTime(),
                updated_at: new DateTime(),
            )
            ->onDatabase($database)
            ->execute();

        // Create some test timesheet entries
        $timesheets = [
            [
                'user_id' => $userId,
                'booking_year' => 2024,
                'week_number' => 1,
                'date' => new DateTime('2024-01-01'),
                'employee_id' => 12345,
                'hours_worked' => 8.0,
                'hours_worked_type' => 'G',
                'created_at' => new DateTime(),
                'updated_at' => new DateTime(),
            ],
            [
                'user_id' => $userId,
                'booking_year' => 2024,
                'week_number' => 1,
                'date' => new DateTime('2024-01-02'),
                'employee_id' => 12345,
                'hours_worked' => 7.5,
                'hours_worked_type' => 'G',
                'created_at' => new DateTime(),
                'updated_at' => new DateTime(),
            ],
        ];

        foreach ($timesheets as $timesheet) {
            query(Timesheet::class)
                ->insert(...$timesheet)
                ->onDatabase($database)
                ->execute();
        }
    }
}
