<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Architecture Overview - 365 Manage Hours</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 1rem;
        }

        nav a {
            color: #4b5563;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s;
        }

        nav a:hover, nav a.active {
            color: #2563eb;
            background-color: #eff6ff;
        }

        main {
            padding: 2rem 0;
        }

        .documentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f7fafc;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: #4299e1;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .doc-header {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .doc-header h1 {
            font-size: 2.5rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .doc-subtitle {
            font-size: 1.25rem;
            color: #718096;
            margin-bottom: 1rem;
        }

        .doc-section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .doc-section:last-child {
            border-bottom: none;
        }

        .doc-section h2 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h3 {
            color: #4a5568;
            font-size: 1.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h4 {
            color: #718096;
            font-size: 1.25rem;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .pattern-card {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }

        .pattern-card h4 {
            color: #2d3748;
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .pattern-card p {
            color: #4a5568;
            margin-bottom: 1rem;
        }

        .pattern-card ul {
            color: #4a5568;
            padding-left: 1.5rem;
        }

        .pattern-card li {
            margin-bottom: 0.5rem;
        }

        .tech-stack {
            background: #e6fffa;
            border-left: 4px solid #38b2ac;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-radius: 8px;
        }

        .tech-stack h3 {
            color: #234e52;
            margin-top: 0;
        }

        .tech-stack ul {
            margin-left: 1rem;
        }

        .tech-stack li {
            color: #2c7a7b;
            margin-bottom: 0.5rem;
        }

        .related-docs {
            background: #e6fffa;
            border-left: 4px solid #38b2ac;
            padding: 1rem;
            margin: 2rem 0;
        }

        .related-docs h4 {
            color: #234e52;
            margin-bottom: 0.5rem;
        }

        .related-docs ul {
            margin-left: 1rem;
        }

        .related-docs a {
            color: #2c7a7b;
            text-decoration: none;
        }

        .related-docs a:hover {
            text-decoration: underline;
        }

        code {
            background: #f1f5f9;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e53e3e;
        }

        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1rem 0;
        }

        pre code {
            background: none;
            padding: 0;
            color: inherit;
        }

        .flow-diagram {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        .flow-diagram h4 {
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .flow-step {
            background: #fff;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 1rem;
            margin: 0.5rem 0;
            position: relative;
        }

        .flow-step::after {
            content: '↓';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            color: #4299e1;
            font-size: 1.2rem;
        }

        .flow-step:last-child::after {
            display: none;
        }

        @media (max-width: 768px) {
            .documentation {
                padding: 1rem;
            }

            .doc-header h1 {
                font-size: 2rem;
            }

            .architecture-grid {
                grid-template-columns: 1fr;
            }

            nav ul {
                flex-direction: column;
                gap: 0.5rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="index.html">Documentation</a></li>
                        <li><a href="api.html">API</a></li>
                        <li><a href="architecture.html" class="active">Architecture</a></li>
                        <li><a href="database.html">Database</a></li>
                        <li><a href="authentication.html">Auth</a></li>
                        <li><a href="testing.html">Testing</a></li>
                        <li><a href="setup.html">Setup</a></li>
                        <li><a href="examples.html">Examples</a></li>
                        <li><a href="validation.html">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="breadcrumb">
                    <a href="index.html">← Back to Main Documentation</a> |
                    <a href="api.html">API Documentation →</a> |
                    <a href="database.html">Database Schema →</a>
                </div>

                <div class="doc-header">
                    <h1>Architecture Overview</h1>
                    <p class="doc-subtitle">System design, patterns, and component interactions</p>
                </div>

                <div class="related-docs">
                    <h4>Related Documentation:</h4>
                    <ul>
                        <li><a href="database.html">Database Schema</a> - Detailed database design</li>
                        <li><a href="examples.html">Code Examples</a> - Implementation patterns</li>
                        <li><a href="testing.html">Testing Documentation</a> - Testing architecture</li>
                    </ul>
                </div>

                <section class="doc-section">
                    <h2>Application Overview</h2>
                    <p>The 365 Manage Hours application is a timesheet management system built with the Tempest PHP framework. It follows clean architecture principles with clear separation of concerns and modern PHP development practices.</p>
                </section>

                <section class="doc-section">
                    <h2>Technology Stack</h2>
                    <div class="tech-stack">
                        <h3>Core Technologies</h3>
                        <ul>
                            <li><strong>Backend:</strong> PHP 8.4+ with Tempest Framework</li>
                            <li><strong>Database:</strong> SQLite with Tempest ORM</li>
                            <li><strong>CSV Processing:</strong> League CSV for robust file handling</li>
                            <li><strong>Testing:</strong> PHPUnit with comprehensive test coverage</li>
                            <li><strong>Frontend:</strong> Server-side rendered views with modern CSS</li>
                        </ul>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Architectural Patterns</h2>

                    <div class="architecture-grid">
                        <div class="pattern-card">
                            <h4>Repository Pattern</h4>
                            <p>Data access is abstracted through repository classes that provide a clean API for database operations:</p>
                            <ul>
                                <li><code>UserRepository</code> - User management operations</li>
                                <li><code>TimesheetRepository</code> - Timesheet CRUD operations</li>
                            </ul>
                            <pre><code>// Example repository usage
$user = $this->userRepository->findByEmail($email);
$timesheets = $this->timesheetRepository
    ->getPaginatedForUser($userId, $page);</code></pre>
                        </div>

                        <div class="pattern-card">
                            <h4>Service Layer</h4>
                            <p>Business logic is encapsulated in service classes:</p>
                            <ul>
                                <li><code>SessionService</code> - Session management and authentication</li>
                                <li><code>ViewService</code> - Centralized view rendering</li>
                            </ul>
                            <pre><code>// Example service usage
$this->sessionService->login($userId, $email, $name);
return $this->viewService->dashboard([
    'timesheets' => $timesheets
]);</code></pre>
                        </div>

                        <div class="pattern-card">
                            <h4>Request Validation</h4>
                            <p>Tempest's native Request classes handle validation with PHP attributes:</p>
                            <ul>
                                <li><code>LoginRequest</code> - Authentication validation</li>
                                <li><code>RegisterRequest</code> - Registration validation</li>
                                <li><code>TimesheetRequest</code> - Timesheet validation</li>
                                <li><code>CsvUploadRequest</code> - File upload validation</li>
                            </ul>
                        </div>

                        <div class="pattern-card">
                            <h4>Middleware System</h4>
                            <p>Cross-cutting concerns are handled by middleware:</p>
                            <ul>
                                <li><code>AuthMiddleware</code> - Protects authenticated routes</li>
                                <li><code>GuestMiddleware</code> - Restricts access for authenticated users</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Component Architecture</h2>

                    <h3>Controllers</h3>
                    <p>Controllers follow the single responsibility principle and use dependency injection:</p>
                    <ul>
                        <li><code>Auth/LoginController.php</code> - Authentication handling</li>
                        <li><code>Auth/RegisterController.php</code> - User registration</li>
                        <li><code>BaseController.php</code> - Shared controller functionality</li>
                        <li><code>DashboardController.php</code> - Main dashboard</li>
                        <li><code>TimesheetController.php</code> - Timesheet CRUD operations</li>
                        <li><code>ProcessCsvController.php</code> - CSV import processing</li>
                        <li><code>DownloadTimesheetController.php</code> - CSV export</li>
                    </ul>

                    <h3>Models</h3>
                    <p>Database models use Tempest's ORM with relationship definitions:</p>
                    <pre><code>// User model with relationship
final class User
{
    use IsDatabaseModel;

    #[HasMany(relationJoin: 'user_id', ownerJoin: 'id')]
    public array $timesheets = [];
}

// Timesheet model with relationship
final class Timesheet
{
    use IsDatabaseModel;

    #[BelongsTo(ownerJoin: 'user_id', relationJoin: 'id')]
    public ?User $user = null;
}</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Request Flow</h2>

                    <div class="flow-diagram">
                        <h4>Authentication Flow</h4>
                        <div class="flow-step">1. User submits login form</div>
                        <div class="flow-step">2. <code>GuestMiddleware</code> checks if user is already authenticated</div>
                        <div class="flow-step">3. <code>LoginController</code> validates credentials using <code>LoginRequest</code></div>
                        <div class="flow-step">4. <code>UserRepository</code> verifies user credentials</div>
                        <div class="flow-step">5. <code>SessionService</code> creates authenticated session</div>
                        <div class="flow-step">6. Redirect to dashboard</div>
                    </div>

                    <div class="flow-diagram">
                        <h4>Timesheet Management Flow</h4>
                        <div class="flow-step">1. User accesses timesheet edit page</div>
                        <div class="flow-step">2. <code>AuthMiddleware</code> verifies authentication</div>
                        <div class="flow-step">3. <code>TimesheetController</code> loads timesheet using <code>TimesheetRepository</code></div>
                        <div class="flow-step">4. User submits form with updates</div>
                        <div class="flow-step">5. <code>TimesheetRequest</code> validates input data</div>
                        <div class="flow-step">6. Business rules validation applied</div>
                        <div class="flow-step">7. <code>TimesheetRepository</code> updates database</div>
                        <div class="flow-step">8. Redirect with success message</div>
                    </div>

                    <div class="flow-diagram">
                        <h4>CSV Processing Flow</h4>
                        <div class="flow-step">1. User uploads CSV file</div>
                        <div class="flow-step">2. <code>CsvUploadRequest</code> validates file format and content</div>
                        <div class="flow-step">3. <code>ProcessCsvController</code> processes CSV using League CSV</div>
                        <div class="flow-step">4. Data mapped through <code>Csv</code> class to model attributes</div>
                        <div class="flow-step">5. <code>TimesheetRepository</code> bulk creates timesheet records</div>
                        <div class="flow-step">6. Success/error feedback to user</div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Security Architecture</h2>

                    <h3>Authentication</h3>
                    <ul>
                        <li>Session-based authentication with secure session management</li>
                        <li>Password hashing using PHP's <code>password_hash()</code> with default algorithm</li>
                        <li>Session timeout (24 hours) with activity tracking</li>
                        <li>Middleware-based route protection</li>
                    </ul>

                    <h3>Input Validation</h3>
                    <ul>
                        <li>Multi-layer validation: Request classes + business rules</li>
                        <li>SQL injection prevention through parameterized queries</li>
                        <li>File upload validation (type, size, content)</li>
                        <li>CSRF protection (handled by Tempest framework)</li>
                    </ul>

                    <h3>Data Protection</h3>
                    <ul>
                        <li>User data isolation (users can only access their own timesheets)</li>
                        <li>Proper error handling without information disclosure</li>
                        <li>Secure file handling for CSV uploads</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>Database Design</h2>

                    <h3>Schema Overview</h3>
                    <pre><code>-- Users table
CREATE TABLE users (
    id INTEGER PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    email_verified_at DATETIME,
    password TEXT NOT NULL,
    remember_token TEXT,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- Timesheets table
CREATE TABLE timesheets (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    booking_year INTEGER NOT NULL,
    week_number INTEGER NOT NULL,
    date DATE NOT NULL,
    employee_id INTEGER NOT NULL,
    hours_worked REAL NOT NULL,
    hours_worked_type TEXT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);</code></pre>

                    <h3>Relationships</h3>
                    <ul>
                        <li><strong>One-to-Many:</strong> User → Timesheets</li>
                        <li><strong>Foreign Key:</strong> timesheets.user_id → users.id</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>Configuration Management</h2>

                    <h3>Environment Configuration</h3>
                    <ul>
                        <li>Database configuration in <code>app/Config/database.config.php</code></li>
                        <li>Testing environment uses in-memory SQLite</li>
                        <li>Production uses file-based SQLite</li>
                    </ul>

                    <h3>Dependency Management</h3>
                    <ul>
                        <li>Composer for PHP dependencies</li>
                        <li>Autoloading with PSR-4 standards</li>
                        <li>Development dependencies separated from production</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>Deployment Architecture</h2>

                    <h3>Requirements</h3>
                    <ul>
                        <li>PHP 8.4 or higher</li>
                        <li>SQLite support</li>
                        <li>Composer for dependency management</li>
                        <li>Web server (Apache, Nginx, or PHP built-in)</li>
                    </ul>

                    <h3>File Structure</h3>
                    <pre><code>365-manage-hours/
├── app/                 # Application code
├── database/           # Database files and migrations
├── public/             # Web root directory
├── tests/              # Test suites
├── vendor/             # Composer dependencies
├── composer.json       # Dependency configuration
└── phpunit.xml        # Testing configuration</code></pre>
                </section>
            </div>
        </div>
    </main>
</body>
</html>
