<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing Documentation - 365 Manage Hours</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        header { background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5rem; font-weight: bold; color: #2563eb; text-decoration: none; }
        nav ul { list-style: none; display: flex; gap: 1rem; }
        nav a { color: #4b5563; text-decoration: none; font-weight: 500; padding: 0.5rem 1rem; border-radius: 4px; transition: all 0.2s; }
        nav a:hover, nav a.active { color: #2563eb; background-color: #eff6ff; }
        main { padding: 2rem 0; }
        .documentation { max-width: 1200px; margin: 0 auto; padding: 2rem; line-height: 1.6; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .breadcrumb { margin-bottom: 2rem; padding: 1rem; background: #f7fafc; border-radius: 8px; font-size: 0.9rem; }
        .breadcrumb a { color: #4299e1; text-decoration: none; font-weight: 500; }
        .breadcrumb a:hover { text-decoration: underline; }
        .doc-header { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 2px solid #e2e8f0; }
        .doc-header h1 { font-size: 2.5rem; color: #2d3748; margin-bottom: 0.5rem; }
        .doc-subtitle { font-size: 1.25rem; color: #718096; margin-bottom: 1rem; }
        .doc-section { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 1px solid #e2e8f0; }
        .doc-section:last-child { border-bottom: none; }
        .doc-section h2 { color: #2d3748; font-size: 2rem; margin-bottom: 1rem; }
        .doc-section h3 { color: #4a5568; font-size: 1.5rem; margin-top: 2rem; margin-bottom: 1rem; }
        .doc-section h4 { color: #718096; font-size: 1.25rem; margin-top: 1.5rem; margin-bottom: 0.75rem; }
        .related-docs { background: #e6fffa; border-left: 4px solid #38b2ac; padding: 1rem; margin: 2rem 0; }
        .related-docs h4 { color: #234e52; margin-bottom: 0.5rem; }
        .related-docs ul { margin-left: 1rem; }
        .related-docs a { color: #2c7a7b; text-decoration: none; }
        .related-docs a:hover { text-decoration: underline; }
        .test-feature { background: #f0fff4; border-left: 4px solid #48bb78; padding: 1.5rem; margin: 1.5rem 0; border-radius: 8px; }
        .test-feature h4 { color: #22543d; margin-top: 0; }
        .test-feature ul { margin-left: 1rem; }
        .test-feature li { color: #2f855a; margin-bottom: 0.5rem; }
        code { background: #f1f5f9; padding: 0.2rem 0.4rem; border-radius: 4px; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.9em; color: #e53e3e; }
        pre { background: #2d3748; color: #e2e8f0; padding: 1rem; border-radius: 8px; overflow-x: auto; margin: 1rem 0; }
        pre code { background: none; padding: 0; color: inherit; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin: 1.5rem 0; }
        .test-card { background: #f7fafc; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #4299e1; }
        .test-card h4 { color: #2d3748; margin-top: 0; margin-bottom: 1rem; }
        .test-card p { color: #4a5568; margin-bottom: 1rem; }
        .test-card ul { color: #4a5568; padding-left: 1.5rem; }
        .test-card li { margin-bottom: 0.5rem; }
        @media (max-width: 768px) { .documentation { padding: 1rem; } .doc-header h1 { font-size: 2rem; } .test-grid { grid-template-columns: 1fr; } nav ul { flex-direction: column; gap: 0.5rem; } .header-content { flex-direction: column; gap: 1rem; } }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="docs/index.html">Documentation</a></li>
                        <li><a href="docs/api.html">API</a></li>
                        <li><a href="docs/architecture.html">Architecture</a></li>
                        <li><a href="docs/database.html">Database</a></li>
                        <li><a href="docs/authentication.html">Auth</a></li>
                        <li><a href="docs/testing.html" class="active">Testing</a></li>
                        <li><a href="docs/setup.html">Setup</a></li>
                        <li><a href="docs/examples.html">Examples</a></li>
                        <li><a href="docs/validation.html">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="breadcrumb">
                    <a href="docs/index.html">← Back to Main Documentation</a> |
                    <a href="docs/authentication.html">Authentication →</a> |
                    <a href="docs/setup.html">Setup Guide →</a>
                </div>

                <div class="doc-header">
                    <h1>Testing Documentation</h1>
                    <p class="doc-subtitle">Comprehensive testing guide covering patterns, utilities, and coverage</p>
                </div>

                <div class="related-docs">
                    <h4>Related Documentation:</h4>
                    <ul>
                        <li><a href="docs/examples.html">Code Examples</a> - Testing patterns and examples</li>
                        <li><a href="docs/setup.html">Setup Guide</a> - Test environment setup</li>
                        <li><a href="docs/architecture.html">Architecture Overview</a> - Testing architecture</li>
                    </ul>
                </div>

                <section class="doc-section">
                    <h2>Overview</h2>
                    <p>The 365 Manage Hours application has a comprehensive test suite built with PHPUnit, following Tempest framework testing patterns. The test suite covers unit tests, feature tests, and integration tests with high coverage of critical functionality.</p>
                </section>

                <section class="doc-section">
                    <h2>Test Structure</h2>

                    <h3>Test Organization</h3>
                    <pre><code>tests/
├── Feature/                    # Feature/Integration tests
│   ├── Auth/                  # Authentication tests
│   │   ├── AuthenticationTest.php
│   │   └── EmailVerificationTest.php
│   ├── Http/                  # HTTP endpoint tests
│   │   ├── Controllers/       # Controller tests
│   │   └── Requests/          # Request validation tests
│   ├── ExampleTest.php        # Basic application tests
│   └── ProfileTest.php        # User profile tests
├── Unit/                      # Unit tests
│   ├── Http/                  # HTTP component tests
│   │   └── Requests/          # Request class tests
│   └── ExampleTest.php        # Basic unit tests
├── Fixtures/                  # Test fixtures and data
└── IntegrationTestCase.php    # Base test case</code></pre>

                    <div class="test-feature">
                        <h4>PHPUnit Configuration (phpunit.xml)</h4>
                        <pre><code>&lt;phpunit bootstrap="vendor/autoload.php" colors="true"&gt;
    &lt;testsuites&gt;
        &lt;testsuite name="Unit"&gt;
            &lt;directory&gt;tests/Unit&lt;/directory&gt;
        &lt;/testsuite&gt;
        &lt;testsuite name="Feature"&gt;
            &lt;directory&gt;tests/Feature&lt;/directory&gt;
        &lt;/testsuite&gt;
    &lt;/testsuites&gt;
    &lt;php&gt;
        &lt;env name="APP_ENV" value="testing"/&gt;
        &lt;env name="APP_DEBUG" value="true"/&gt;
        &lt;env name="DB_PATH" value=":memory:"/&gt;
    &lt;/php&gt;
&lt;/phpunit&gt;</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Testing Patterns</h2>

                    <h3>Base Test Case</h3>
                    <p>All integration tests extend <code>IntegrationTestCase</code>:</p>

                    <pre><code>abstract class IntegrationTestCase extends IntegrationTest
{
    protected string $root = __DIR__ . '/../';

    protected function setUp(): void
    {
        parent::setUp();

        // Force testing environment
        $appConfig = $this->container->get(\Tempest\Core\AppConfig::class);
        $reflection = new \ReflectionProperty($appConfig, 'environment');
        $reflection->setAccessible(true);
        $reflection->setValue($appConfig, \Tempest\Core\Environment::TESTING);

        // Fresh database for each test
        $migrationManager = $this->container->get(MigrationManager::class);
        $migrationManager->dropAll();
        $migrationManager->up();
    }
}</code></pre>

                    <div class="test-feature">
                        <h4>Database Testing</h4>
                        <ul>
                            <li><strong>Fresh Database Per Test:</strong> Uses in-memory SQLite (<code>:memory:</code>)</li>
                            <li><strong>Clean State:</strong> Runs fresh migrations for each test</li>
                            <li><strong>No Persistence:</strong> No data persistence between tests</li>
                        </ul>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Test Categories</h2>

                    <div class="test-grid">
                        <div class="test-card">
                            <h4>Unit Tests</h4>
                            <p>Test individual components in isolation:</p>
                            <ul>
                                <li>Request validation classes</li>
                                <li>Business logic methods</li>
                                <li>Data transformation</li>
                                <li>Utility functions</li>
                            </ul>
                            <pre><code>class TimesheetRequestTest extends TestCase
{
    #[Test]
    public function validateBusinessRules_returns_empty_array_for_valid_data(): void
    {
        $request = new TimesheetRequest(
            booking_year: '2024',
            week_number: '25',
            date: '2024-06-15',
            employee_id: '123',
            hours_worked: '8.0',
            hours_worked_type: 'REGULAR'
        );

        $errors = $request->validateBusinessRules();
        $this->assertEmpty($errors);
    }
}</code></pre>
                        </div>

                        <div class="test-card">
                            <h4>Feature Tests</h4>
                            <p>Test complete application workflows:</p>
                            <ul>
                                <li>HTTP endpoint testing</li>
                                <li>Authentication flows</li>
                                <li>Form submissions</li>
                                <li>File uploads</li>
                            </ul>
                            <pre><code>public function test_can_update_a_timesheet(): void
{
    [$user, $timesheetId] = $this->createUserAndTimesheet();

    $updateData = [
        'booking_year' => '2024',
        'week_number' => '30',
        'date' => '2024-07-20',
        'employee_id' => '320',
        'hours_worked' => '7.5',
        'hours_worked_type' => 'TZFG100',
    ];

    $response = $this->http->post("/timesheets/{$timesheetId}", $updateData);
    $response->assertRedirect();

    // Verify database update
    $updatedTimesheet = query(Timesheet::class)
        ->select()
        ->where('id = ?', $timesheetId)
        ->first();

    $this->assertEquals(2024, $updatedTimesheet->booking_year);
}</code></pre>
                        </div>

                        <div class="test-card">
                            <h4>Integration Tests</h4>
                            <p>Test component interactions:</p>
                            <ul>
                                <li>Database operations</li>
                                <li>Service integrations</li>
                                <li>Full application flows</li>
                                <li>Middleware interactions</li>
                            </ul>
                        </div>

                        <div class="test-card">
                            <h4>Validation Testing</h4>
                            <p>Comprehensive validation coverage:</p>
                            <ul>
                                <li>Required field validation</li>
                                <li>Data type validation</li>
                                <li>Business rule validation</li>
                                <li>Boundary value testing</li>
                                <li>File upload validation</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Authentication Testing</h2>

                    <h3>Session Management in Tests</h3>
                    <pre><code>// Authenticate user for testing
$sessionService = $this->container->get(SessionService::class);
$sessionService->login((int) $user->id, $user->email, $user->name);

// Verify authentication
$this->assertTrue($sessionService->isAuthenticated());</code></pre>

                    <h3>Testing Protected Routes</h3>
                    <pre><code>public function test_dashboard_requires_authentication(): void
{
    // Test without authentication
    $response = $this->http->get('/dashboard');
    $response->assertRedirect('/login');

    // Test with authentication
    $this->authenticateUser();
    $response = $this->http->get('/dashboard');
    $response->assertOk();
}</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Test Utilities</h2>

                    <h3>Helper Methods</h3>
                    <pre><code>// Create user and timesheet for testing
private function createUserAndTimesheet(): array
{
    $user = new User(
        name: 'Test User',
        email: '<EMAIL>',
        password: password_hash('password', PASSWORD_DEFAULT),
    );
    $user->save();

    $timesheetId = query(Timesheet::class)->insert(/* ... */)->execute();

    $sessionService = $this->container->get(SessionService::class);
    $sessionService->login((int) $user->id, $user->email, $user->name);

    return [$user, $timesheetId];
}</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Running Tests</h2>

                    <h3>Command Line</h3>
                    <pre><code># Run all tests
composer test
# or
phpunit

# Run specific test suite
phpunit --testsuite=Unit
phpunit --testsuite=Feature

# Run specific test file
phpunit tests/Feature/Auth/AuthenticationTest.php

# Run with coverage
phpunit --coverage-html coverage/</code></pre>

                    <div class="test-feature">
                        <h4>Test Environment</h4>
                        <p>Tests automatically use:</p>
                        <ul>
                            <li><strong>Environment:</strong> <code>testing</code></li>
                            <li><strong>Database:</strong> In-memory SQLite</li>
                            <li><strong>Debug Mode:</strong> Enabled</li>
                            <li><strong>Fresh Migrations:</strong> For each test</li>
                        </ul>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Test Coverage</h2>

                    <div class="test-grid">
                        <div class="test-card">
                            <h4>Authentication System (95%+)</h4>
                            <ul>
                                <li>Login/logout flows</li>
                                <li>Registration process</li>
                                <li>Session management</li>
                                <li>Middleware protection</li>
                            </ul>
                        </div>

                        <div class="test-card">
                            <h4>Timesheet Management (90%+)</h4>
                            <ul>
                                <li>CRUD operations</li>
                                <li>Validation rules</li>
                                <li>Business logic</li>
                                <li>User isolation</li>
                            </ul>
                        </div>

                        <div class="test-card">
                            <h4>CSV Processing (85%+)</h4>
                            <ul>
                                <li>File upload validation</li>
                                <li>Data processing</li>
                                <li>Export functionality</li>
                            </ul>
                        </div>

                        <div class="test-card">
                            <h4>Request Validation (95%+)</h4>
                            <ul>
                                <li>All request classes</li>
                                <li>Business rules</li>
                                <li>Edge cases</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Testing Best Practices</h2>

                    <div class="test-feature">
                        <h4>Best Practices Applied</h4>
                        <ul>
                            <li><strong>Isolation:</strong> Each test is independent</li>
                            <li><strong>Descriptive Names:</strong> Clear test method names</li>
                            <li><strong>Arrange-Act-Assert:</strong> Consistent test structure</li>
                            <li><strong>Data Providers:</strong> Parameterized testing for multiple scenarios</li>
                            <li><strong>Helper Methods:</strong> Reusable test utilities</li>
                            <li><strong>Database State:</strong> Fresh state for each test</li>
                            <li><strong>Error Cases:</strong> Testing both success and failure paths</li>
                        </ul>
                    </div>
                </section>
            </div>
        </div>
    </main>
</body>
</html>
