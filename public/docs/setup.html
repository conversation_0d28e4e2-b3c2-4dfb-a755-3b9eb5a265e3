<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup & Configuration Guide - 365 Manage Hours</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        header { background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5rem; font-weight: bold; color: #2563eb; text-decoration: none; }
        nav ul { list-style: none; display: flex; gap: 1rem; }
        nav a { color: #4b5563; text-decoration: none; font-weight: 500; padding: 0.5rem 1rem; border-radius: 4px; transition: all 0.2s; }
        nav a:hover, nav a.active { color: #2563eb; background-color: #eff6ff; }
        main { padding: 2rem 0; }
        .documentation { max-width: 1200px; margin: 0 auto; padding: 2rem; line-height: 1.6; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .breadcrumb { margin-bottom: 2rem; padding: 1rem; background: #f7fafc; border-radius: 8px; font-size: 0.9rem; }
        .breadcrumb a { color: #4299e1; text-decoration: none; font-weight: 500; }
        .breadcrumb a:hover { text-decoration: underline; }
        .doc-header { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 2px solid #e2e8f0; }
        .doc-header h1 { font-size: 2.5rem; color: #2d3748; margin-bottom: 0.5rem; }
        .doc-subtitle { font-size: 1.25rem; color: #718096; margin-bottom: 1rem; }
        .doc-section { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 1px solid #e2e8f0; }
        .doc-section:last-child { border-bottom: none; }
        .doc-section h2 { color: #2d3748; font-size: 2rem; margin-bottom: 1rem; }
        .doc-section h3 { color: #4a5568; font-size: 1.5rem; margin-top: 2rem; margin-bottom: 1rem; }
        .doc-section h4 { color: #718096; font-size: 1.25rem; margin-top: 1.5rem; margin-bottom: 0.75rem; }
        .related-docs { background: #e6fffa; border-left: 4px solid #38b2ac; padding: 1rem; margin: 2rem 0; }
        .related-docs h4 { color: #234e52; margin-bottom: 0.5rem; }
        .related-docs ul { margin-left: 1rem; }
        .related-docs a { color: #2c7a7b; text-decoration: none; }
        .related-docs a:hover { text-decoration: underline; }
        .setup-step { background: #f0fff4; border-left: 4px solid #48bb78; padding: 1.5rem; margin: 1.5rem 0; border-radius: 8px; }
        .setup-step h4 { color: #22543d; margin-top: 0; }
        .setup-step ul { margin-left: 1rem; }
        .setup-step li { color: #2f855a; margin-bottom: 0.5rem; }
        .warning { background: #fef5e7; border-left: 4px solid #ed8936; padding: 1.5rem; margin: 1.5rem 0; border-radius: 8px; }
        .warning h4 { color: #c05621; margin-top: 0; }
        .warning p { color: #744210; }
        code { background: #f1f5f9; padding: 0.2rem 0.4rem; border-radius: 4px; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.9em; color: #e53e3e; }
        pre { background: #2d3748; color: #e2e8f0; padding: 1rem; border-radius: 8px; overflow-x: auto; margin: 1rem 0; }
        pre code { background: none; padding: 0; color: inherit; }
        .requirements-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem; margin: 1.5rem 0; }
        .requirement-card { background: #f7fafc; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #4299e1; }
        .requirement-card h4 { color: #2d3748; margin-top: 0; margin-bottom: 1rem; }
        .requirement-card ul { color: #4a5568; padding-left: 1.5rem; }
        .requirement-card li { margin-bottom: 0.5rem; }
        @media (max-width: 768px) { .documentation { padding: 1rem; } .doc-header h1 { font-size: 2rem; } .requirements-grid { grid-template-columns: 1fr; } nav ul { flex-direction: column; gap: 0.5rem; } .header-content { flex-direction: column; gap: 1rem; } }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="docs/index.html">Documentation</a></li>
                        <li><a href="docs/api.html">API</a></li>
                        <li><a href="docs/architecture.html">Architecture</a></li>
                        <li><a href="docs/database.html">Database</a></li>
                        <li><a href="docs/authentication.html">Auth</a></li>
                        <li><a href="docs/testing.html">Testing</a></li>
                        <li><a href="docs/setup.html" class="active">Setup</a></li>
                        <li><a href="docs/examples.html">Examples</a></li>
                        <li><a href="docs/validation.html">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="breadcrumb">
                    <a href="docs/index.html">← Back to Main Documentation</a> |
                    <a href="docs/testing.html">Testing →</a> |
                    <a href="docs/examples.html">Code Examples →</a>
                </div>

                <div class="doc-header">
                    <h1>Setup & Configuration Guide</h1>
                    <p class="doc-subtitle">Complete installation and deployment guide for development and production</p>
                </div>

                <div class="related-docs">
                    <h4>Related Documentation:</h4>
                    <ul>
                        <li><a href="docs/database.html">Database Schema</a> - Database configuration details</li>
                        <li><a href="docs/architecture.html">Architecture Overview</a> - Deployment architecture</li>
                        <li><a href="docs/testing.html">Testing Documentation</a> - Test environment setup</li>
                    </ul>
                </div>

                <section class="doc-section">
                    <h2>System Requirements</h2>

                    <div class="requirements-grid">
                        <div class="requirement-card">
                            <h4>Minimum Requirements</h4>
                            <ul>
                                <li><strong>PHP:</strong> 8.4 or higher</li>
                                <li><strong>Extensions:</strong> PDO SQLite, mbstring, fileinfo, openssl</li>
                                <li><strong>Composer:</strong> Latest version</li>
                                <li><strong>Web Server:</strong> Apache, Nginx, or PHP built-in server</li>
                                <li><strong>Storage:</strong> 100MB free disk space</li>
                            </ul>
                        </div>

                        <div class="requirement-card">
                            <h4>Recommended Environment</h4>
                            <ul>
                                <li><strong>PHP:</strong> 8.4+ with OPcache enabled</li>
                                <li><strong>Memory:</strong> 256MB PHP memory limit</li>
                                <li><strong>Web Server:</strong> Nginx or Apache with mod_rewrite</li>
                                <li><strong>SSL:</strong> HTTPS enabled for production</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Installation</h2>

                    <div class="setup-step">
                        <h4>1. Clone Repository</h4>
                        <pre><code>git clone https://github.com/your-username/365-manage-hours.git
cd 365-manage-hours</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>2. Install Dependencies</h4>
                        <pre><code># Install PHP dependencies
composer install

# For production, use optimized autoloader
composer install --optimize-autoloader --no-dev</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>3. Database Setup</h4>
                        <pre><code># Create database directory
mkdir -p database

# Run database migrations
./tempest migrate

# Optional: Seed test data
./tempest db:seed TestDataSeeder</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>4. File Permissions</h4>
                        <pre><code># Set proper permissions
chmod -R 755 storage/
chmod -R 755 database/
chmod +x tempest</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Web Server Configuration</h2>

                    <h3>Apache Configuration</h3>
                    <p>Create <code>.htaccess</code> in the <code>public</code> directory:</p>

                    <pre><code>&lt;IfModule mod_rewrite.c&gt;
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)$ index.php [QSA,L]
&lt;/IfModule&gt;

# Security headers
&lt;IfModule mod_headers.c&gt;
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
&lt;/IfModule&gt;</code></pre>

                    <p>Virtual host configuration:</p>
                    <pre><code>&lt;VirtualHost *:80&gt;
    ServerName 365-manage-hours.local
    DocumentRoot /path/to/365-manage-hours/public

    &lt;Directory /path/to/365-manage-hours/public&gt;
        AllowOverride All
        Require all granted
    &lt;/Directory&gt;

    ErrorLog ${APACHE_LOG_DIR}/365-manage-hours_error.log
    CustomLog ${APACHE_LOG_DIR}/365-manage-hours_access.log combined
&lt;/VirtualHost&gt;</code></pre>

                    <h3>Nginx Configuration</h3>
                    <pre><code>server {
    listen 80;
    server_name 365-manage-hours.local;
    root /path/to/365-manage-hours/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.4-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
    }

    location ~ /(database|tests|vendor) {
        deny all;
    }
}</code></pre>

                    <h3>PHP Built-in Server (Development Only)</h3>
                    <pre><code># Start development server
composer serve
# or
php -S localhost:8000 -t public</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Development Setup</h2>

                    <div class="setup-step">
                        <h4>1. Development Dependencies</h4>
                        <pre><code># Install development dependencies
composer install

# Install with development tools
composer require --dev phpunit/phpunit</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>2. Running Tests</h4>
                        <pre><code># Run all tests
composer test

# Run specific test suite
./vendor/bin/phpunit --testsuite=Unit
./vendor/bin/phpunit --testsuite=Feature

# Run with coverage
./vendor/bin/phpunit --coverage-html coverage/</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>3. Development Server</h4>
                        <pre><code># Start development server
composer serve

# Access application
open http://localhost:8000</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>4. Database Management</h4>
                        <pre><code># Run migrations
./tempest migrate

# Fresh migration (drops all tables)
./tempest migrate:fresh

# Seed test data
./tempest db:seed TestDataSeeder</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Production Deployment</h2>

                    <div class="setup-step">
                        <h4>1. Server Preparation</h4>
                        <pre><code># Update system
sudo apt update && sudo apt upgrade -y

# Install PHP 8.4 and extensions
sudo apt install php8.4 php8.4-fpm php8.4-sqlite3 php8.4-mbstring php8.4-fileinfo

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>2. Application Deployment</h4>
                        <pre><code># Clone and setup
git clone https://github.com/your-username/365-manage-hours.git
cd 365-manage-hours

# Install production dependencies
composer install --optimize-autoloader --no-dev

# Set up database
./tempest migrate

# Set permissions
sudo chown -R www-data:www-data storage/ database/
sudo chmod -R 755 storage/ database/</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>3. Security Configuration</h4>
                        <pre><code># Set secure file permissions
find . -type f -exec chmod 644 {} \;
find . -type d -exec chmod 755 {} \;
chmod +x tempest

# Protect sensitive directories
echo "Deny from all" > database/.htaccess
echo "Deny from all" > vendor/.htaccess</code></pre>
                    </div>

                    <div class="warning">
                        <h4>Security Note</h4>
                        <p>Always use HTTPS in production and ensure proper file permissions are set. Never expose sensitive directories like <code>database/</code> or <code>vendor/</code> to the web.</p>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Configuration</h2>

                    <h3>Database Configuration</h3>
                    <p>Edit <code>app/Config/database.config.php</code>:</p>

                    <pre><code>&lt;?php

use Tempest\Database\Config\SQLiteConfig;

return new SQLiteConfig(
    path: __DIR__ . '/../../database/database.sqlite',
);</code></pre>

                    <h3>Environment Configuration</h3>
                    <pre><code># Development environment
export APP_ENV=development
export APP_DEBUG=true
export DB_PATH=/path/to/database.sqlite

# Production environment
export APP_ENV=production
export APP_DEBUG=false
export DB_PATH=/path/to/production/database.sqlite</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Troubleshooting</h2>

                    <h3>Common Issues</h3>

                    <div class="setup-step">
                        <h4>Database Connection Errors</h4>
                        <pre><code># Check database file permissions
ls -la database/database.sqlite

# Verify SQLite extension
php -m | grep sqlite</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>File Permission Issues</h4>
                        <pre><code># Fix permissions
sudo chown -R www-data:www-data .
sudo chmod -R 755 storage/ database/</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>Composer Issues</h4>
                        <pre><code># Clear Composer cache
composer clear-cache

# Update dependencies
composer update</code></pre>
                    </div>

                    <div class="setup-step">
                        <h4>Web Server Issues</h4>
                        <pre><code># Check Apache/Nginx error logs
sudo tail -f /var/log/apache2/error.log
sudo tail -f /var/log/nginx/error.log

# Test PHP configuration
php -v
php --ini</code></pre>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Support</h2>

                    <p>For additional support:</p>
                    <ul>
                        <li>Check the application logs in <code>log/debug.log</code></li>
                        <li>Review the test suite for usage examples</li>
                        <li>Consult the Tempest framework documentation</li>
                        <li>Check GitHub issues for known problems</li>
                    </ul>

                    <div class="setup-step">
                        <h4>Useful Commands</h4>
                        <pre><code># Development server
composer serve

# Run tests
composer test

# Database migrations
./tempest migrate

# Generate documentation
./tempest static:generate</code></pre>
                    </div>
                </section>
            </div>
        </div>
    </main>
</body>
</html>
