<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>365 Manage Hours - Developer Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 1rem;
        }

        nav a {
            color: #4b5563;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s;
        }

        nav a:hover, nav a.active {
            color: #2563eb;
            background-color: #eff6ff;
        }

        main {
            padding: 2rem 0;
        }

        .documentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .doc-header {
            text-align: center;
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .doc-header h1 {
            font-size: 3rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .doc-subtitle {
            font-size: 1.25rem;
            color: #718096;
            margin-bottom: 1rem;
        }

        .doc-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            font-size: 0.9rem;
            color: #a0aec0;
        }

        .breadcrumb {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f7fafc;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: #4299e1;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .doc-nav {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }

        .doc-nav h2 {
            margin-top: 0;
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .doc-nav ul {
            list-style: none;
            padding: 0;
        }

        .doc-nav li {
            margin-bottom: 0.5rem;
        }

        .doc-nav a {
            color: #4299e1;
            text-decoration: none;
            font-weight: 500;
            display: block;
            padding: 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .doc-nav a:hover {
            background-color: #e2e8f0;
            text-decoration: none;
        }

        .doc-section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .doc-section:last-child {
            border-bottom: none;
        }

        .doc-section h2 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h3 {
            color: #4a5568;
            font-size: 1.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h4 {
            color: #718096;
            font-size: 1.25rem;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .feature-card {
            background: #f7fafc;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #4299e1;
        }

        .feature-card h4 {
            color: #2d3748;
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .feature-card p {
            color: #4a5568;
            margin-bottom: 1rem;
        }

        .feature-card ul {
            color: #4a5568;
            padding-left: 1.5rem;
        }

        .feature-card li {
            margin-bottom: 0.5rem;
        }

        code {
            background: #f1f5f9;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e53e3e;
        }

        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1rem 0;
        }

        pre code {
            background: none;
            padding: 0;
            color: inherit;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: #2563eb;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.2s;
            margin: 0.25rem;
        }

        .btn:hover {
            background: #1d4ed8;
        }

        .btn-secondary {
            background: #6b7280;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        @media (max-width: 768px) {
            .documentation {
                padding: 1rem;
            }

            .doc-header h1 {
                font-size: 2rem;
            }

            .doc-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            nav ul {
                flex-direction: column;
                gap: 0.5rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="docs/index.html" class="active">Documentation</a></li>
                        <li><a href="docs/api.html">API</a></li>
                        <li><a href="docs/architecture.html">Architecture</a></li>
                        <li><a href="docs/database.html">Database</a></li>
                        <li><a href="docs/authentication.html">Auth</a></li>
                        <li><a href="docs/testing.html">Testing</a></li>
                        <li><a href="docs/setup.html">Setup</a></li>
                        <li><a href="docs/examples.html">Examples</a></li>
                        <li><a href="docs/validation.html">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="doc-header">
                    <h1>365 Manage Hours</h1>
                    <p class="doc-subtitle">Developer Documentation</p>
                    <div class="doc-meta">
                        <span class="version">Version 1.0</span>
                        <span class="updated">Last updated: January 2025</span>
                    </div>
                </div>

                <section class="doc-section">
                    <h2>Overview</h2>
                    <p>Welcome to the comprehensive developer documentation for the 365 Manage Hours timesheet management application. This documentation provides everything you need to understand, develop, and maintain the application.</p>
                </section>

                <section class="doc-section">
                    <h2>Application Summary</h2>
                    <p>365 Manage Hours is a modern timesheet management system built with the Tempest PHP framework. It allows users to:</p>
                    <ul>
                        <li><strong>Register and authenticate</strong> with secure session management</li>
                        <li><strong>Upload CSV timesheet files</strong> with validation and processing</li>
                        <li><strong>Edit individual timesheet records</strong> with comprehensive validation</li>
                        <li><strong>Export timesheet data</strong> as CSV files</li>
                        <li><strong>Manage personal timesheet data</strong> with user isolation</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>Technology Stack</h2>
                    <ul>
                        <li><strong>Backend</strong>: PHP 8.4+ with Tempest Framework</li>
                        <li><strong>Database</strong>: SQLite with Tempest ORM</li>
                        <li><strong>CSV Processing</strong>: League CSV library</li>
                        <li><strong>Testing</strong>: PHPUnit with comprehensive coverage</li>
                        <li><strong>Frontend</strong>: Server-side rendered views</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>Documentation Structure</h2>

                    <h3>📚 Core Documentation</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4><a href="docs/api.html">API Documentation</a></h4>
                            <p>Complete API reference covering all endpoints, request/response formats, validation rules, and authentication requirements.</p>
                            <ul>
                                <li>Authentication endpoints (login, register, logout)</li>
                                <li>Dashboard and timesheet management</li>
                                <li>CSV processing (upload/export)</li>
                                <li>Request/response examples</li>
                                <li>Error handling patterns</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h4><a href="docs/architecture.html">Architecture Overview</a></h4>
                            <p>Detailed explanation of the application's architecture, design patterns, and component interactions.</p>
                            <ul>
                                <li>Architectural patterns (Repository, Service Layer, Request Validation)</li>
                                <li>Component structure and relationships</li>
                                <li>Request flow diagrams</li>
                                <li>Security architecture</li>
                                <li>Database design principles</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h4><a href="docs/database.html">Database Schema</a></h4>
                            <p>Comprehensive database documentation including schema, relationships, and data access patterns.</p>
                            <ul>
                                <li>Table definitions and relationships</li>
                                <li>Column specifications and constraints</li>
                                <li>Migration management</li>
                                <li>Query patterns and examples</li>
                                <li>Performance considerations</li>
                            </ul>
                        </div>
                    </div>

                    <h3>🔐 Security & Authentication</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4><a href="docs/authentication.html">Authentication & Authorization</a></h4>
                            <p>Complete guide to the authentication system, session management, and security features.</p>
                            <ul>
                                <li>Session-based authentication flow</li>
                                <li>Password security and hashing</li>
                                <li>Middleware protection system</li>
                                <li>User authorization and data isolation</li>
                                <li>Security best practices</li>
                            </ul>
                        </div>
                    </div>

                    <h3>🧪 Testing & Quality</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4><a href="docs/testing.html">Testing Documentation</a></h4>
                            <p>Comprehensive testing guide covering patterns, utilities, and coverage.</p>
                            <ul>
                                <li>Test structure and organization</li>
                                <li>Testing patterns and best practices</li>
                                <li>Database testing with fresh migrations</li>
                                <li>Authentication testing patterns</li>
                                <li>Validation testing coverage</li>
                            </ul>
                        </div>
                    </div>

                    <h3>🚀 Setup & Deployment</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4><a href="docs/setup.html">Setup & Configuration Guide</a></h4>
                            <p>Complete installation and deployment guide for development and production environments.</p>
                            <ul>
                                <li>System requirements and dependencies</li>
                                <li>Local development setup</li>
                                <li>Web server configuration (Apache/Nginx)</li>
                                <li>Production deployment strategies</li>
                                <li>Google Cloud Platform deployment</li>
                                <li>Troubleshooting guide</li>
                            </ul>
                        </div>
                    </div>

                    <h3>💻 Development</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4><a href="docs/examples.html">Code Examples & Usage Patterns</a></h4>
                            <p>Practical code examples and patterns for common development tasks.</p>
                            <ul>
                                <li>Controller patterns and best practices</li>
                                <li>Repository implementation examples</li>
                                <li>Request validation patterns</li>
                                <li>Service layer usage</li>
                                <li>Database query examples</li>
                                <li>CSV processing patterns</li>
                                <li>Testing examples</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h4><a href="docs/validation.html">Validation Reference</a></h4>
                            <p>Comprehensive reference for all validation rules, business logic, and data constraints.</p>
                            <ul>
                                <li>Request validation classes</li>
                                <li>Database constraints</li>
                                <li>CSV data validation</li>
                                <li>Validation layers</li>
                                <li>Error handling patterns</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Quick Start</h2>

                    <h3>For New Developers</h3>
                    <ol>
                        <li><strong>Start with <a href="docs/setup.html">Setup Guide</a></strong> - Get the application running locally</li>
                        <li><strong>Review <a href="docs/architecture.html">Architecture Overview</a></strong> - Understand the system design</li>
                        <li><strong>Study <a href="docs/examples.html">Code Examples</a></strong> - Learn common patterns</li>
                        <li><strong>Explore <a href="docs/api.html">API Documentation</a></strong> - Understand the endpoints</li>
                    </ol>

                    <h3>For API Integration</h3>
                    <ol>
                        <li><strong>Read <a href="docs/api.html">API Documentation</a></strong> - Complete endpoint reference</li>
                        <li><strong>Check <a href="docs/authentication.html">Authentication Guide</a></strong> - Understand auth requirements</li>
                        <li><strong>Review <a href="docs/database.html">Database Schema</a></strong> - Understand data structure</li>
                    </ol>

                    <h3>For Testing & QA</h3>
                    <ol>
                        <li><strong>Study <a href="docs/testing.html">Testing Documentation</a></strong> - Understand test patterns</li>
                        <li><strong>Review <a href="docs/examples.html">Code Examples</a></strong> - See testing examples</li>
                        <li><strong>Check <a href="docs/setup.html">Setup Guide</a></strong> - Environment configuration</li>
                    </ol>
                </section>

                <section class="doc-section">
                    <h2>Key Features</h2>

                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4>🔒 Secure Authentication</h4>
                            <ul>
                                <li>Session-based authentication with 24-hour timeout</li>
                                <li>Password hashing with PHP's secure defaults</li>
                                <li>Middleware-based route protection</li>
                                <li>User data isolation</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h4>📊 CSV Processing</h4>
                            <ul>
                                <li>Robust file upload validation (type, size, format)</li>
                                <li>Semicolon-delimited CSV support</li>
                                <li>Bulk data import with error handling</li>
                                <li>CSV export functionality</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h4>✅ Comprehensive Validation</h4>
                            <ul>
                                <li>Multi-layer validation system</li>
                                <li>Tempest Request classes with PHP attributes</li>
                                <li>Business rule validation</li>
                                <li>File upload validation</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h4>🧪 Extensive Testing</h4>
                            <ul>
                                <li>95%+ test coverage on critical paths</li>
                                <li>Feature tests for all endpoints</li>
                                <li>Unit tests for validation logic</li>
                                <li>Integration tests with fresh database</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h4>🏗️ Clean Architecture</h4>
                            <ul>
                                <li>Repository pattern for data access</li>
                                <li>Service layer for business logic</li>
                                <li>Dependency injection throughout</li>
                                <li>Separation of concerns</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Support & Resources</h2>

                    <h3>Getting Help</h3>
                    <ul>
                        <li>Review existing documentation first</li>
                        <li>Check test files for usage examples</li>
                        <li>Examine existing code patterns</li>
                        <li>Consult Tempest framework documentation</li>
                    </ul>

                    <h3>Useful Commands</h3>
                    <pre><code># Development server
composer serve

# Run tests
composer test

# Database migrations
./tempest migrate

# Generate documentation
./tempest static:generate</code></pre>
                </section>
            </div>
        </div>
    </main>
</body>
</html>
