<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Examples & Usage Patterns - 365 Manage Hours</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        header { background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5rem; font-weight: bold; color: #2563eb; text-decoration: none; }
        nav ul { list-style: none; display: flex; gap: 1rem; }
        nav a { color: #4b5563; text-decoration: none; font-weight: 500; padding: 0.5rem 1rem; border-radius: 4px; transition: all 0.2s; }
        nav a:hover, nav a.active { color: #2563eb; background-color: #eff6ff; }
        main { padding: 2rem 0; }
        .documentation { max-width: 1200px; margin: 0 auto; padding: 2rem; line-height: 1.6; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .breadcrumb { margin-bottom: 2rem; padding: 1rem; background: #f7fafc; border-radius: 8px; font-size: 0.9rem; }
        .breadcrumb a { color: #4299e1; text-decoration: none; font-weight: 500; }
        .breadcrumb a:hover { text-decoration: underline; }
        .doc-header { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 2px solid #e2e8f0; }
        .doc-header h1 { font-size: 2.5rem; color: #2d3748; margin-bottom: 0.5rem; }
        .doc-subtitle { font-size: 1.25rem; color: #718096; margin-bottom: 1rem; }
        .doc-section { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 1px solid #e2e8f0; }
        .doc-section:last-child { border-bottom: none; }
        .doc-section h2 { color: #2d3748; font-size: 2rem; margin-bottom: 1rem; }
        .doc-section h3 { color: #4a5568; font-size: 1.5rem; margin-top: 2rem; margin-bottom: 1rem; }
        .doc-section h4 { color: #718096; font-size: 1.25rem; margin-top: 1.5rem; margin-bottom: 0.75rem; }
        .related-docs { background: #e6fffa; border-left: 4px solid #38b2ac; padding: 1rem; margin: 2rem 0; }
        .related-docs h4 { color: #234e52; margin-bottom: 0.5rem; }
        .related-docs ul { margin-left: 1rem; }
        .related-docs a { color: #2c7a7b; text-decoration: none; }
        .related-docs a:hover { text-decoration: underline; }
        .example-card { background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; margin: 1.5rem 0; overflow: hidden; }
        .example-header { background: #4a5568; color: #fff; padding: 1rem; font-weight: 600; }
        .example-content { padding: 1.5rem; }
        .example-description { margin-bottom: 1rem; color: #4a5568; }
        code { background: #f1f5f9; padding: 0.2rem 0.4rem; border-radius: 4px; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.9em; color: #e53e3e; }
        pre { background: #2d3748; color: #e2e8f0; padding: 1rem; border-radius: 8px; overflow-x: auto; margin: 1rem 0; }
        pre code { background: none; padding: 0; color: inherit; }
        .pattern-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem; margin: 1.5rem 0; }
        .pattern-card { background: #f7fafc; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #4299e1; }
        .pattern-card h4 { color: #2d3748; margin-top: 0; margin-bottom: 1rem; }
        .pattern-card p { color: #4a5568; margin-bottom: 1rem; }
        @media (max-width: 768px) { .documentation { padding: 1rem; } .doc-header h1 { font-size: 2rem; } .pattern-grid { grid-template-columns: 1fr; } nav ul { flex-direction: column; gap: 0.5rem; } .header-content { flex-direction: column; gap: 1rem; } }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="index.html">Documentation</a></li>
                        <li><a href="api.html">API</a></li>
                        <li><a href="architecture.html">Architecture</a></li>
                        <li><a href="database.html">Database</a></li>
                        <li><a href="authentication.html">Auth</a></li>
                        <li><a href="testing.html">Testing</a></li>
                        <li><a href="setup.html">Setup</a></li>
                        <li><a href="examples.html" class="active">Examples</a></li>
                        <li><a href="validation.html">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="breadcrumb">
                    <a href="index.html">← Back to Main Documentation</a> | 
                    <a href="setup.html">Setup Guide →</a> | 
                    <a href="api.html">API Documentation →</a>
                </div>

                <div class="doc-header">
                    <h1>Code Examples & Usage Patterns</h1>
                    <p class="doc-subtitle">Practical code examples and patterns for common development tasks</p>
                </div>

                <div class="related-docs">
                    <h4>Related Documentation:</h4>
                    <ul>
                        <li><a href="architecture.html">Architecture Overview</a> - Design patterns and principles</li>
                        <li><a href="api.html">API Documentation</a> - Endpoint implementations</li>
                        <li><a href="testing.html">Testing Documentation</a> - Testing examples</li>
                    </ul>
                </div>

                <section class="doc-section">
                    <h2>Overview</h2>
                    <p>This document provides practical code examples and usage patterns for working with the 365 Manage Hours application. These examples demonstrate best practices and common patterns used throughout the codebase.</p>
                </section>

                <section class="doc-section">
                    <h2>Controller Patterns</h2>
                    
                    <div class="example-card">
                        <div class="example-header">Basic Controller Structure</div>
                        <div class="example-content">
                            <div class="example-description">
                                Standard controller pattern with dependency injection and base functionality.
                            </div>
                            <pre><code>&lt;?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Services\SessionService;
use App\Services\ViewService;
use App\Repositories\TimesheetRepository;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\View\View;

final readonly class ExampleController extends BaseController
{
    public function __construct(
        SessionService $sessionService,
        private TimesheetRepository $timesheetRepository,
        private ViewService $viewService
    ) {
        parent::__construct($sessionService);
    }

    #[Get('/example')]
    public function index(): View
    {
        $userId = $this->getAuthenticatedUserId();
        $data = $this->timesheetRepository->getAllForUser($userId);
        
        return $this->viewService->render('example/index', [
            'data' => $data
        ]);
    }
}</code></pre>
                        </div>
                    </div>

                    <div class="example-card">
                        <div class="example-header">Request Handling with Validation</div>
                        <div class="example-content">
                            <div class="example-description">
                                Complete request handling pattern with validation, error handling, and response generation.
                            </div>
                            <pre><code>#[Post('/timesheets/{id}', middleware: [AuthMiddleware::class])]
public function update(int $id, Request $request): View|Response
{
    try {
        $userId = $this->getAuthenticatedUserId();
        
        // Find and verify ownership
        $timesheet = $this->timesheetRepository->findByIdForUser($id, $userId);
        if (!$timesheet) {
            return $this->redirectWithError('/dashboard', 'Timesheet not found');
        }

        // Create and validate request
        $timesheetRequest = new TimesheetRequest(
            booking_year: $request->body['booking_year'] ?? '',
            week_number: $request->body['week_number'] ?? '',
            date: $request->body['date'] ?? '',
            employee_id: $request->body['employee_id'] ?? '',
            hours_worked: $request->body['hours_worked'] ?? '',
            hours_worked_type: $request->body['hours_worked_type'] ?? ''
        );

        // Validate input
        $errors = $this->validateTimesheetRequest($timesheetRequest);
        if (!empty($errors)) {
            return $this->viewService->withErrors('timesheet/edit', $errors, [
                'timesheet' => $timesheet,
                'old' => $request->body
            ]);
        }

        // Update timesheet
        $updateData = [
            'booking_year' => (int) $timesheetRequest->booking_year,
            'week_number' => (int) $timesheetRequest->week_number,
            'date' => new \DateTime($timesheetRequest->date),
            'employee_id' => (int) $timesheetRequest->employee_id,
            'hours_worked' => (float) $timesheetRequest->hours_worked,
            'hours_worked_type' => $timesheetRequest->hours_worked_type,
        ];

        $success = $this->timesheetRepository->update($id, $updateData);
        
        if (!$success) {
            return $this->redirectWithError('/dashboard', 'Failed to update timesheet');
        }

        return $this->redirectWithSuccess('/dashboard', 'Timesheet updated successfully');

    } catch (AuthenticationException $e) {
        return $this->redirectWithError('/login', 'Please log in to continue');
    } catch (\Exception $e) {
        return $this->redirectWithError('/dashboard', 'An error occurred while updating the timesheet');
    }
}</code></pre>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Repository Patterns</h2>
                    
                    <div class="example-card">
                        <div class="example-header">Basic Repository Structure</div>
                        <div class="example-content">
                            <div class="example-description">
                                Repository pattern implementation with data access methods and user isolation.
                            </div>
                            <pre><code>&lt;?php

namespace App\Repositories;

use App\Models\Timesheet;
use DateTime;
use function Tempest\Database\query;

final readonly class TimesheetRepository
{
    /**
     * Find timesheet by ID for specific user (ensures data isolation)
     */
    public function findByIdForUser(int $id, int $userId): ?Timesheet
    {
        return query(Timesheet::class)
            ->select()
            ->where('id = ? AND user_id = ?', $id, $userId)
            ->first();
    }

    /**
     * Get paginated results with metadata
     */
    public function getPaginatedForUser(int $userId, int $page = 1, int $perPage = 25): array
    {
        $offset = ($page - 1) * $perPage;

        $timesheets = query(Timesheet::class)
            ->select()
            ->where('user_id = ?', $userId)
            ->orderBy('date DESC')
            ->limit($perPage)
            ->offset($offset)
            ->all();

        $totalCount = query('timesheets')
            ->count()
            ->where('user_id = ?', $userId)
            ->execute();

        return [
            'timesheets' => $timesheets,
            'totalCount' => $totalCount,
            'totalPages' => ceil($totalCount / $perPage),
            'currentPage' => $page,
            'hasNextPage' => $page < ceil($totalCount / $perPage),
            'hasPrevPage' => $page > 1,
        ];
    }

    /**
     * Create with automatic timestamps
     */
    public function create(array $data): int
    {
        $timesheetData = array_merge($data, [
            'created_at' => new DateTime(),
            'updated_at' => new DateTime(),
        ]);

        return query(Timesheet::class)
            ->insert(...$timesheetData)
            ->execute();
    }

    /**
     * Update with automatic timestamp
     */
    public function update(int $id, array $data): bool
    {
        $updateData = array_merge($data, ['updated_at' => new DateTime()]);
        
        $result = query(Timesheet::class)
            ->update(...$updateData)
            ->where('id = ?', $id)
            ->execute();

        return $result !== false;
    }
}</code></pre>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Request Validation Patterns</h2>
                    
                    <div class="example-card">
                        <div class="example-header">Basic Request Class</div>
                        <div class="example-content">
                            <div class="example-description">
                                Request validation class with Tempest attributes and custom business rules.
                            </div>
                            <pre><code>&lt;?php

namespace App\Http\Requests;

use Tempest\Http\IsRequest;
use Tempest\Http\Request;
use Tempest\Validation\Rules\Numeric;

final class TimesheetRequest implements Request
{
    use IsRequest;

    public function __construct(
        #[Numeric]
        public string $booking_year,

        #[Numeric]
        public string $week_number,

        public string $date,

        #[Numeric]
        public string $employee_id,

        #[Numeric]
        public string $hours_worked,

        public string $hours_worked_type,
    ) {}

    /**
     * Custom business rules validation
     */
    public function validateBusinessRules(): array
    {
        $errors = [];

        // Check if properties are initialized (validation passed)
        if (!isset($this->booking_year) || !isset($this->week_number)) {
            return $errors; // Skip business rules if basic validation failed
        }

        // Validate booking year range
        $year = (int) $this->booking_year;
        if ($year < 1900 || $year > 2100) {
            $errors['booking_year'] = 'Booking year must be between 1900 and 2100';
        }

        // Validate week number range
        $week = (int) $this->week_number;
        if ($week < 1 || $week > 53) {
            $errors['week_number'] = 'Week number must be between 1 and 53';
        }

        // Validate date format
        if (!empty($this->date)) {
            $dateTime = \DateTime::createFromFormat('Y-m-d', $this->date);
            if (!$dateTime || $dateTime->format('Y-m-d') !== $this->date) {
                $errors['date'] = 'Date must be in YYYY-MM-DD format';
            }
        }

        // Validate hours worked
        if (isset($this->hours_worked)) {
            $hours = (float) $this->hours_worked;
            if ($hours < 0) {
                $errors['hours_worked'] = 'Hours worked cannot be negative';
            }
        }

        return $errors;
    }
}</code></pre>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Service Patterns</h2>
                    
                    <div class="pattern-grid">
                        <div class="pattern-card">
                            <h4>Session Service Usage</h4>
                            <pre><code>// Check authentication
if (!$this->sessionService->isAuthenticated()) {
    return $this->redirectWithError('/login', 'Please log in');
}

// Get user data
$user = $this->sessionService->getUser();
$userId = $this->sessionService->getUserId();

// Login user
$this->sessionService->login($userId, $email, $name);
// or with User model
$this->sessionService->loginWithUser($user);

// Logout
$this->sessionService->logout();</code></pre>
                        </div>

                        <div class="pattern-card">
                            <h4>View Service Usage</h4>
                            <pre><code>// Basic view rendering
return $this->viewService->render('dashboard', [
    'timesheets' => $timesheets,
    'user' => $user
]);

// Render with errors
return $this->viewService->withErrors('timesheet/edit', $errors, [
    'timesheet' => $timesheet,
    'old' => $oldInput
]);

// Render with single error message
return $this->viewService->withError('dashboard', 'An error occurred', [
    'timesheets' => []
]);</code></pre>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Database Query Patterns</h2>
                    
                    <div class="example-card">
                        <div class="example-header">Basic Queries</div>
                        <div class="example-content">
                            <pre><code>// Find single record
$user = query(User::class)
    ->select()
    ->where('email = ?', $email)
    ->first();

// Find multiple records with conditions
$timesheets = query(Timesheet::class)
    ->select()
    ->where('user_id = ? AND booking_year = ?', $userId, $year)
    ->orderBy('date DESC')
    ->all();

// Count records
$count = query('timesheets')
    ->count()
    ->where('user_id = ?', $userId)
    ->execute();

// Insert single record
$id = query(User::class)
    ->insert(
        name: $name,
        email: $email,
        password: password_hash($password, PASSWORD_DEFAULT),
        created_at: new DateTime(),
        updated_at: new DateTime()
    )
    ->execute();

// Update with conditions
$result = query(Timesheet::class)
    ->update(
        hours_worked: $newHours,
        updated_at: new DateTime()
    )
    ->where('id = ? AND user_id = ?', $id, $userId)
    ->execute();</code></pre>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>CSV Processing Patterns</h2>
                    
                    <div class="example-card">
                        <div class="example-header">CSV Import</div>
                        <div class="example-content">
                            <pre><code>use League\Csv\Reader;
use App\Csv;

// Read CSV file
$reader = Reader::createFromPath($file['tmp_name']);
$reader->setDelimiter(';');
$reader->setHeaderOffset(0);

$records = $reader->getRecords();
$timesheetData = [];

foreach ($records as $record) {
    try {
        // Map CSV data to application model
        $csv = new Csv(
            Boekjaar: (int) $record['Boekjaar'],
            Week: (int) $record['Week'],
            Datum: new DateTime($record['Datum']),
            Persnr: (int) $record['Persnr'],
            Uren: $record['Uren'],
            Uurcode: $record['Uurcode']
        );

        if ($csv->validate()) {
            $attributes = $csv->toModelAttributes();
            $attributes['user_id'] = $userId;
            $timesheetData[] = $attributes;
        }
    } catch (Exception $e) {
        // Log error and continue
        continue;
    }
}

// Bulk create records
$processedCount = $this->timesheetRepository->bulkCreate($timesheetData);</code></pre>
                        </div>
                    </div>

                    <div class="example-card">
                        <div class="example-header">CSV Export</div>
                        <div class="example-content">
                            <pre><code>use League\Csv\Writer;

// Create CSV content
$csv = Writer::createFromString();

// Add headers
$headers = ['Boekjaar', 'Week', 'Datum', 'Persnr', 'Uren', 'Uurcode'];
$csv->insertOne($headers);

// Add data rows
foreach ($timesheets as $timesheet) {
    $row = [
        $timesheet->booking_year,
        $timesheet->week_number,
        $timesheet->date->format('Y-m-d'),
        $timesheet->employee_id,
        $timesheet->hours_worked,
        $timesheet->hours_worked_type,
    ];
    $csv->insertOne($row);
}

// Return as download response
return new class($csv->toString(), $filename) implements Response {
    public function __construct(
        private string $content,
        private string $filename
    ) {}

    public function send(): void
    {
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $this->filename . '"');
        header('Content-Length: ' . strlen($this->content));
        echo $this->content;
    }
};</code></pre>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Testing Patterns</h2>
                    
                    <div class="example-card">
                        <div class="example-header">Feature Test Example</div>
                        <div class="example-content">
                            <pre><code>public function test_can_update_timesheet(): void
{
    // Arrange
    [$user, $timesheetId] = $this->createUserAndTimesheet();
    
    $updateData = [
        'booking_year' => '2024',
        'week_number' => '30',
        'date' => '2024-07-20',
        'employee_id' => '320',
        'hours_worked' => '7.5',
        'hours_worked_type' => 'REGULAR',
    ];

    // Act
    $response = $this->http->post("/timesheets/{$timesheetId}", $updateData);

    // Assert
    $response->assertRedirect();
    
    $updatedTimesheet = query(Timesheet::class)
        ->select()
        ->where('id = ?', $timesheetId)
        ->first();
    
    $this->assertEquals(2024, $updatedTimesheet->booking_year);
    $this->assertEquals(30, $updatedTimesheet->week_number);
}

private function createUserAndTimesheet(): array
{
    $user = new User(
        name: 'Test User',
        email: '<EMAIL>',
        password: password_hash('password', PASSWORD_DEFAULT),
    );
    $user->save();

    $timesheetId = query(Timesheet::class)
        ->insert(
            user_id: (int) $user->id,
            booking_year: 2023,
            week_number: 25,
            date: new DateTime('2023-06-15'),
            employee_id: 310,
            hours_worked: 8.0,
            hours_worked_type: 'REGULAR',
            created_at: new DateTime(),
            updated_at: new DateTime(),
        )
        ->execute();

    $sessionService = $this->container->get(SessionService::class);
    $sessionService->login((int) $user->id, $user->email, $user->name);

    return [$user, $timesheetId];
}</code></pre>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </main>
</body>
</html>
