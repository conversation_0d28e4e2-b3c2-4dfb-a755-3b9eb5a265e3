<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Schema - 365 Manage Hours</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="index.html">Documentation</a></li>
                        <li><a href="api.html">API</a></li>
                        <li><a href="architecture.html">Architecture</a></li>
                        <li><a href="database.html" class="active">Database</a></li>
                        <li><a href="authentication.html">Auth</a></li>
                        <li><a href="testing.html">Testing</a></li>
                        <li><a href="setup.html">Setup</a></li>
                        <li><a href="examples.html">Examples</a></li>
                        <li><a href="validation.html">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="breadcrumb">
                    <a href="index.html">← Back to Main Documentation</a> | 
                    <a href="architecture.html">Architecture →</a> | 
                    <a href="authentication.html">Authentication →</a>
                </div>

                <div class="doc-header">
                    <h1>Database Schema Documentation</h1>
                    <p class="doc-subtitle">Complete database structure, relationships, and data access patterns</p>
                </div>

                <div class="related-docs">
                    <h4>Related Documentation:</h4>
                    <ul>
                        <li><a href="architecture.html">Architecture Overview</a> - Database layer architecture</li>
                        <li><a href="examples.html">Code Examples</a> - Query patterns and examples</li>
                        <li><a href="setup.html">Setup Guide</a> - Database configuration</li>
                    </ul>
                </div>

                <section class="doc-section">
                    <h2>Overview</h2>
                    <p>The 365 Manage Hours application uses SQLite as its database engine with Tempest ORM for data access. The schema is designed to support user authentication and timesheet management with proper relationships and constraints.</p>
                    
                    <div class="tech-stack">
                        <h3>Database Configuration</h3>
                        <ul>
                            <li><strong>Engine:</strong> SQLite</li>
                            <li><strong>Location:</strong> <code>database/database.sqlite</code></li>
                            <li><strong>Testing:</strong> In-memory SQLite (<code>:memory:</code>)</li>
                            <li><strong>ORM:</strong> Tempest Database with query builder</li>
                        </ul>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Schema Overview</h2>
                    <p>The database consists of two main tables with a one-to-many relationship:</p>
                    <pre><code>users (1) ──── (many) timesheets</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Table Definitions</h2>
                    
                    <h3>users</h3>
                    <p>Stores user account information for authentication and identification.</p>
                    
                    <pre><code>CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    email_verified_at DATETIME NULL,
    password TEXT NOT NULL,
    remember_token TEXT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- Indexes
CREATE UNIQUE INDEX idx_users_email ON users(email);</code></pre>

                    <div class="table-details">
                        <h4>Column Details</h4>
                        <table class="schema-table">
                            <thead>
                                <tr>
                                    <th>Column</th>
                                    <th>Type</th>
                                    <th>Constraints</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>id</code></td>
                                    <td>INTEGER</td>
                                    <td>PRIMARY KEY, AUTO INCREMENT</td>
                                    <td>Unique user identifier</td>
                                </tr>
                                <tr>
                                    <td><code>name</code></td>
                                    <td>TEXT</td>
                                    <td>NOT NULL</td>
                                    <td>User's display name</td>
                                </tr>
                                <tr>
                                    <td><code>email</code></td>
                                    <td>TEXT</td>
                                    <td>NOT NULL, UNIQUE</td>
                                    <td>User's email address (login identifier)</td>
                                </tr>
                                <tr>
                                    <td><code>email_verified_at</code></td>
                                    <td>DATETIME</td>
                                    <td>NULLABLE</td>
                                    <td>Email verification timestamp</td>
                                </tr>
                                <tr>
                                    <td><code>password</code></td>
                                    <td>TEXT</td>
                                    <td>NOT NULL</td>
                                    <td>Hashed password using PHP's password_hash()</td>
                                </tr>
                                <tr>
                                    <td><code>remember_token</code></td>
                                    <td>TEXT</td>
                                    <td>NULLABLE</td>
                                    <td>Token for "remember me" functionality</td>
                                </tr>
                                <tr>
                                    <td><code>created_at</code></td>
                                    <td>DATETIME</td>
                                    <td>NOT NULL</td>
                                    <td>Account creation timestamp</td>
                                </tr>
                                <tr>
                                    <td><code>updated_at</code></td>
                                    <td>DATETIME</td>
                                    <td>NOT NULL</td>
                                    <td>Last modification timestamp</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <h3>timesheets</h3>
                    <p>Stores timesheet entries with work hours and related metadata.</p>
                    
                    <pre><code>CREATE TABLE timesheets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    booking_year INTEGER NOT NULL,
    week_number INTEGER NOT NULL,
    date DATE NOT NULL,
    employee_id INTEGER NOT NULL,
    hours_worked REAL NOT NULL,
    hours_worked_type TEXT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Indexes
CREATE INDEX idx_timesheets_user_id ON timesheets(user_id);
CREATE INDEX idx_timesheets_date ON timesheets(date);
CREATE INDEX idx_timesheets_user_date ON timesheets(user_id, date);</code></pre>

                    <div class="table-details">
                        <h4>Column Details</h4>
                        <table class="schema-table">
                            <thead>
                                <tr>
                                    <th>Column</th>
                                    <th>Type</th>
                                    <th>Constraints</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>id</code></td>
                                    <td>INTEGER</td>
                                    <td>PRIMARY KEY, AUTO INCREMENT</td>
                                    <td>Unique timesheet entry identifier</td>
                                </tr>
                                <tr>
                                    <td><code>user_id</code></td>
                                    <td>INTEGER</td>
                                    <td>NOT NULL, FOREIGN KEY</td>
                                    <td>Reference to users.id</td>
                                </tr>
                                <tr>
                                    <td><code>booking_year</code></td>
                                    <td>INTEGER</td>
                                    <td>NOT NULL</td>
                                    <td>Year for timesheet entry</td>
                                </tr>
                                <tr>
                                    <td><code>week_number</code></td>
                                    <td>INTEGER</td>
                                    <td>NOT NULL</td>
                                    <td>Week number (1-53)</td>
                                </tr>
                                <tr>
                                    <td><code>date</code></td>
                                    <td>DATE</td>
                                    <td>NOT NULL</td>
                                    <td>Specific date of work</td>
                                </tr>
                                <tr>
                                    <td><code>employee_id</code></td>
                                    <td>INTEGER</td>
                                    <td>NOT NULL</td>
                                    <td>Employee identifier</td>
                                </tr>
                                <tr>
                                    <td><code>hours_worked</code></td>
                                    <td>REAL</td>
                                    <td>NOT NULL</td>
                                    <td>Number of hours worked</td>
                                </tr>
                                <tr>
                                    <td><code>hours_worked_type</code></td>
                                    <td>TEXT</td>
                                    <td>NOT NULL</td>
                                    <td>Type/category of hours worked</td>
                                </tr>
                                <tr>
                                    <td><code>created_at</code></td>
                                    <td>DATETIME</td>
                                    <td>NOT NULL</td>
                                    <td>Record creation timestamp</td>
                                </tr>
                                <tr>
                                    <td><code>updated_at</code></td>
                                    <td>DATETIME</td>
                                    <td>NOT NULL</td>
                                    <td>Last modification timestamp</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>
            </div>
        </div>
    </main>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 1rem;
        }

        nav a {
            color: #4b5563;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s;
        }

        nav a:hover, nav a.active {
            color: #2563eb;
            background-color: #eff6ff;
        }

        main {
            padding: 2rem 0;
        }

        .documentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f7fafc;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: #4299e1;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .doc-header {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .doc-header h1 {
            font-size: 2.5rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .doc-subtitle {
            font-size: 1.25rem;
            color: #718096;
            margin-bottom: 1rem;
        }

        .doc-section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .doc-section:last-child {
            border-bottom: none;
        }

        .doc-section h2 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h3 {
            color: #4a5568;
            font-size: 1.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h4 {
            color: #718096;
            font-size: 1.25rem;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .tech-stack {
            background: #e6fffa;
            border-left: 4px solid #38b2ac;
            padding: 1.5rem;
            margin: 1.5rem 0;
            border-radius: 8px;
        }

        .tech-stack h3 {
            color: #234e52;
            margin-top: 0;
        }

        .tech-stack ul {
            margin-left: 1rem;
        }

        .tech-stack li {
            color: #2c7a7b;
            margin-bottom: 0.5rem;
        }

        .related-docs {
            background: #e6fffa;
            border-left: 4px solid #38b2ac;
            padding: 1rem;
            margin: 2rem 0;
        }

        .related-docs h4 {
            color: #234e52;
            margin-bottom: 0.5rem;
        }

        .related-docs ul {
            margin-left: 1rem;
        }

        .related-docs a {
            color: #2c7a7b;
            text-decoration: none;
        }

        .related-docs a:hover {
            text-decoration: underline;
        }

        code {
            background: #f1f5f9;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e53e3e;
        }

        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1rem 0;
        }

        pre code {
            background: none;
            padding: 0;
            color: inherit;
        }

        .schema-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
            background: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .schema-table th {
            background: #4a5568;
            color: #fff;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
        }

        .schema-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .schema-table tr:last-child td {
            border-bottom: none;
        }

        .schema-table tr:nth-child(even) {
            background: #f7fafc;
        }

        .table-details {
            margin: 1.5rem 0;
        }

        @media (max-width: 768px) {
            .documentation {
                padding: 1rem;
            }

            .doc-header h1 {
                font-size: 2rem;
            }

            nav ul {
                flex-direction: column;
                gap: 0.5rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .schema-table {
                font-size: 0.9rem;
            }

            .schema-table th,
            .schema-table td {
                padding: 0.5rem;
            }
        }
    </style>
</body>
</html>
