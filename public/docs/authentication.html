<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication & Authorization - 365 Manage Hours</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        header { background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5rem; font-weight: bold; color: #2563eb; text-decoration: none; }
        nav ul { list-style: none; display: flex; gap: 1rem; }
        nav a { color: #4b5563; text-decoration: none; font-weight: 500; padding: 0.5rem 1rem; border-radius: 4px; transition: all 0.2s; }
        nav a:hover, nav a.active { color: #2563eb; background-color: #eff6ff; }
        main { padding: 2rem 0; }
        .documentation { max-width: 1200px; margin: 0 auto; padding: 2rem; line-height: 1.6; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .breadcrumb { margin-bottom: 2rem; padding: 1rem; background: #f7fafc; border-radius: 8px; font-size: 0.9rem; }
        .breadcrumb a { color: #4299e1; text-decoration: none; font-weight: 500; }
        .breadcrumb a:hover { text-decoration: underline; }
        .doc-header { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 2px solid #e2e8f0; }
        .doc-header h1 { font-size: 2.5rem; color: #2d3748; margin-bottom: 0.5rem; }
        .doc-subtitle { font-size: 1.25rem; color: #718096; margin-bottom: 1rem; }
        .doc-section { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 1px solid #e2e8f0; }
        .doc-section:last-child { border-bottom: none; }
        .doc-section h2 { color: #2d3748; font-size: 2rem; margin-bottom: 1rem; }
        .doc-section h3 { color: #4a5568; font-size: 1.5rem; margin-top: 2rem; margin-bottom: 1rem; }
        .doc-section h4 { color: #718096; font-size: 1.25rem; margin-top: 1.5rem; margin-bottom: 0.75rem; }
        .related-docs { background: #e6fffa; border-left: 4px solid #38b2ac; padding: 1rem; margin: 2rem 0; }
        .related-docs h4 { color: #234e52; margin-bottom: 0.5rem; }
        .related-docs ul { margin-left: 1rem; }
        .related-docs a { color: #2c7a7b; text-decoration: none; }
        .related-docs a:hover { text-decoration: underline; }
        .security-feature { background: #fef5e7; border-left: 4px solid #ed8936; padding: 1.5rem; margin: 1.5rem 0; border-radius: 8px; }
        .security-feature h4 { color: #c05621; margin-top: 0; }
        .security-feature ul { margin-left: 1rem; }
        .security-feature li { color: #744210; margin-bottom: 0.5rem; }
        code { background: #f1f5f9; padding: 0.2rem 0.4rem; border-radius: 4px; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.9em; color: #e53e3e; }
        pre { background: #2d3748; color: #e2e8f0; padding: 1rem; border-radius: 8px; overflow-x: auto; margin: 1rem 0; }
        pre code { background: none; padding: 0; color: inherit; }
        .flow-step { background: #fff; border: 1px solid #e2e8f0; border-radius: 6px; padding: 1rem; margin: 0.5rem 0; position: relative; }
        .flow-step::after { content: '↓'; position: absolute; bottom: -15px; left: 50%; transform: translateX(-50%); color: #4299e1; font-size: 1.2rem; }
        .flow-step:last-child::after { display: none; }
        @media (max-width: 768px) { .documentation { padding: 1rem; } .doc-header h1 { font-size: 2rem; } nav ul { flex-direction: column; gap: 0.5rem; } .header-content { flex-direction: column; gap: 1rem; } }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="index.html">Documentation</a></li>
                        <li><a href="api.html">API</a></li>
                        <li><a href="architecture.html">Architecture</a></li>
                        <li><a href="database.html">Database</a></li>
                        <li><a href="authentication.html" class="active">Auth</a></li>
                        <li><a href="testing.html">Testing</a></li>
                        <li><a href="setup.html">Setup</a></li>
                        <li><a href="examples.html">Examples</a></li>
                        <li><a href="validation.html">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="breadcrumb">
                    <a href="index.html">← Back to Main Documentation</a> | 
                    <a href="api.html">API Documentation →</a> | 
                    <a href="testing.html">Testing →</a>
                </div>

                <div class="doc-header">
                    <h1>Authentication & Authorization</h1>
                    <p class="doc-subtitle">Session-based authentication system and security features</p>
                </div>

                <div class="related-docs">
                    <h4>Related Documentation:</h4>
                    <ul>
                        <li><a href="api.html">API Documentation</a> - Authentication endpoints</li>
                        <li><a href="architecture.html">Architecture Overview</a> - Security architecture</li>
                        <li><a href="examples.html">Code Examples</a> - Authentication patterns</li>
                    </ul>
                </div>

                <section class="doc-section">
                    <h2>Overview</h2>
                    <p>The 365 Manage Hours application implements a session-based authentication system with role-based access control. Authentication is handled through the <code>SessionService</code> and protected by middleware components.</p>
                </section>

                <section class="doc-section">
                    <h2>Authentication System</h2>
                    
                    <div class="security-feature">
                        <h4>Session-Based Authentication</h4>
                        <p>The application uses PHP sessions managed through Tempest's session handling with the following characteristics:</p>
                        <ul>
                            <li><strong>Session Storage:</strong> Server-side session storage</li>
                            <li><strong>Session Timeout:</strong> 24 hours of inactivity</li>
                            <li><strong>Activity Tracking:</strong> Last activity timestamp updated on each request</li>
                            <li><strong>Secure Logout:</strong> Complete session destruction on logout</li>
                        </ul>
                    </div>

                    <div class="security-feature">
                        <h4>Password Security</h4>
                        <ul>
                            <li><strong>Hashing Algorithm:</strong> PHP's <code>password_hash()</code> with <code>PASSWORD_DEFAULT</code></li>
                            <li><strong>Verification:</strong> <code>password_verify()</code> for credential checking</li>
                            <li><strong>Minimum Length:</strong> 8 characters required</li>
                            <li><strong>No Password Reset:</strong> Currently not implemented (future enhancement)</li>
                        </ul>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Authentication Flow</h2>
                    
                    <h3>Registration Process</h3>
                    <div class="flow-step">1. User submits registration form</div>
                    <div class="flow-step">2. <code>GuestMiddleware</code> ensures user is not already authenticated</div>
                    <div class="flow-step">3. <code>RegisterRequest</code> validates input data</div>
                    <div class="flow-step">4. <code>UserRepository</code> creates new user with hashed password</div>
                    <div class="flow-step">5. <code>SessionService</code> automatically logs in the new user</div>
                    <div class="flow-step">6. Redirect to dashboard</div>

                    <h3>Login Process</h3>
                    <div class="flow-step">1. User submits login credentials</div>
                    <div class="flow-step">2. <code>GuestMiddleware</code> checks if user is already authenticated</div>
                    <div class="flow-step">3. <code>LoginRequest</code> validates input</div>
                    <div class="flow-step">4. <code>UserRepository</code> finds user by email</div>
                    <div class="flow-step">5. Password verification using <code>password_verify()</code></div>
                    <div class="flow-step">6. <code>SessionService</code> creates authenticated session</div>
                    <div class="flow-step">7. Redirect to dashboard</div>

                    <h3>Logout Process</h3>
                    <div class="flow-step">1. User initiates logout (POST request)</div>
                    <div class="flow-step">2. <code>SessionService</code> destroys session data</div>
                    <div class="flow-step">3. Redirect to home page with success message</div>
                </section>

                <section class="doc-section">
                    <h2>Session Management</h2>
                    
                    <h3>SessionService</h3>
                    <p>The <code>SessionService</code> provides centralized session management:</p>
                    
                    <pre><code>final readonly class SessionService
{
    // Session keys
    private const string USER_ID_KEY = 'user_id';
    private const string USER_EMAIL_KEY = 'user_email';
    private const string USER_NAME_KEY = 'user_name';
    private const string LAST_ACTIVITY_KEY = 'last_activity';
}</code></pre>

                    <h4>Key Methods</h4>
                    <pre><code>// Check authentication status
public function isAuthenticated(): bool

// Get authenticated user ID
public function getUserId(): ?int

// Get user data array
public function getUser(): ?array

// Get full user model
public function getUserModel(): ?User

// Login user
public function login(int $userId, string $email, string $name = ''): void
public function loginWithUser(User $user): void

// Logout user
public function logout(): void</code></pre>

                    <div class="security-feature">
                        <h4>Session Security Features</h4>
                        <ul>
                            <li><strong>Activity Tracking:</strong> Updates last activity on each authenticated request</li>
                            <li><strong>Session Timeout:</strong> Automatically expires sessions after 24 hours</li>
                            <li><strong>Session Destruction:</strong> Complete cleanup on logout</li>
                            <li><strong>Type Safety:</strong> Proper type casting for session data</li>
                        </ul>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Authorization & Middleware</h2>
                    
                    <h3>Middleware Components</h3>
                    
                    <h4>AuthMiddleware</h4>
                    <p>Protects routes that require authentication:</p>
                    <pre><code>#[Get('/dashboard', middleware: [AuthMiddleware::class])]
public function dashboard(): View</code></pre>
                    
                    <p><strong>Behavior:</strong></p>
                    <ul>
                        <li>Checks <code>SessionService::isAuthenticated()</code></li>
                        <li>Redirects to login page if not authenticated</li>
                        <li>Allows request to continue if authenticated</li>
                    </ul>

                    <h4>GuestMiddleware</h4>
                    <p>Restricts access for already authenticated users:</p>
                    <pre><code>#[Get('/login', middleware: [GuestMiddleware::class])]
public function show(): View</code></pre>
                    
                    <p><strong>Behavior:</strong></p>
                    <ul>
                        <li>Checks <code>SessionService::isAuthenticated()</code></li>
                        <li>Redirects to dashboard if already authenticated</li>
                        <li>Allows request to continue if not authenticated</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>Route Protection</h2>
                    
                    <h3>Public Routes</h3>
                    <ul>
                        <li><code>GET /</code> - Home page</li>
                        <li><code>GET /login</code> - Login form (guest only)</li>
                        <li><code>POST /login</code> - Login processing (guest only)</li>
                        <li><code>GET /register</code> - Registration form (guest only)</li>
                        <li><code>POST /register</code> - Registration processing (guest only)</li>
                    </ul>

                    <h3>Protected Routes (Require Authentication)</h3>
                    <ul>
                        <li><code>GET /dashboard</code> - User dashboard</li>
                        <li><code>GET /timesheets/{id}/edit</code> - Edit timesheet</li>
                        <li><code>POST /timesheets/{id}</code> - Update timesheet</li>
                        <li><code>POST /process-csv</code> - CSV upload</li>
                        <li><code>GET /export</code> - CSV export</li>
                        <li><code>POST /logout</code> - Logout</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>User Authorization</h2>
                    
                    <div class="security-feature">
                        <h4>Data Isolation</h4>
                        <p>The application implements user-based data isolation:</p>
                        <ul>
                            <li><strong>Timesheet Access:</strong> Users can only access their own timesheets</li>
                            <li><strong>Repository Filtering:</strong> All timesheet queries include user_id filtering</li>
                            <li><strong>Controller Validation:</strong> Controllers verify ownership before operations</li>
                        </ul>
                    </div>

                    <pre><code>// Example: Timesheet access control
public function findByIdForUser(int $id, int $userId): ?Timesheet
{
    return query(Timesheet::class)
        ->select()
        ->where('id = ? AND user_id = ?', $id, $userId)
        ->first();
}</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Security Considerations</h2>
                    
                    <div class="security-feature">
                        <h4>Input Validation</h4>
                        <ul>
                            <li><strong>Email Validation:</strong> Proper email format checking</li>
                            <li><strong>Password Strength:</strong> Minimum length requirements</li>
                            <li><strong>CSRF Protection:</strong> Handled by Tempest framework</li>
                            <li><strong>SQL Injection Prevention:</strong> Parameterized queries</li>
                        </ul>
                    </div>

                    <div class="security-feature">
                        <h4>Session Security</h4>
                        <ul>
                            <li><strong>Session Fixation:</strong> New session on login</li>
                            <li><strong>Session Hijacking:</strong> Activity-based timeout</li>
                            <li><strong>Secure Cookies:</strong> Framework-managed cookie security</li>
                            <li><strong>Session Storage:</strong> Server-side storage only</li>
                        </ul>
                    </div>

                    <div class="security-feature">
                        <h4>Error Handling</h4>
                        <ul>
                            <li><strong>Information Disclosure:</strong> Generic error messages for authentication failures</li>
                            <li><strong>Timing Attacks:</strong> Consistent response times for login attempts</li>
                            <li><strong>Exception Handling:</strong> Proper exception catching and user-friendly messages</li>
                        </ul>
                    </div>
                </section>
            </div>
        </div>
    </main>
</body>
</html>
