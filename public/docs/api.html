<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Documentation - 365 Manage Hours</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 1rem;
        }

        nav a {
            color: #4b5563;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: all 0.2s;
        }

        nav a:hover, nav a.active {
            color: #2563eb;
            background-color: #eff6ff;
        }

        main {
            padding: 2rem 0;
        }

        .documentation {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            margin-bottom: 2rem;
            padding: 1rem;
            background: #f7fafc;
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .breadcrumb a {
            color: #4299e1;
            text-decoration: none;
            font-weight: 500;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .doc-header {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #e2e8f0;
        }

        .doc-header h1 {
            font-size: 2.5rem;
            color: #2d3748;
            margin-bottom: 0.5rem;
        }

        .doc-subtitle {
            font-size: 1.25rem;
            color: #718096;
            margin-bottom: 1rem;
        }

        .doc-section {
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .doc-section:last-child {
            border-bottom: none;
        }

        .doc-section h2 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h3 {
            color: #4a5568;
            font-size: 1.5rem;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }

        .doc-section h4 {
            color: #718096;
            font-size: 1.25rem;
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }

        .endpoint {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin: 1.5rem 0;
            overflow: hidden;
        }

        .endpoint-header {
            background: #2d3748;
            color: #fff;
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .method {
            background: #4299e1;
            color: #fff;
            padding: 0.25rem 0.75rem;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.875rem;
        }

        .method.post {
            background: #48bb78;
        }

        .method.put {
            background: #ed8936;
        }

        .method.delete {
            background: #f56565;
        }

        .endpoint-path {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 1.1rem;
        }

        .endpoint-content {
            padding: 1.5rem;
        }

        .endpoint-description {
            margin-bottom: 1rem;
            color: #4a5568;
        }

        .endpoint-details {
            display: grid;
            gap: 1rem;
        }

        .detail-section {
            background: #fff;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 1rem;
        }

        .detail-section h5 {
            color: #2d3748;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        code {
            background: #f1f5f9;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e53e3e;
        }

        pre {
            background: #2d3748;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1rem 0;
        }

        pre code {
            background: none;
            padding: 0;
            color: inherit;
        }

        .validation-rules {
            background: #fef5e7;
            border-left: 4px solid #ed8936;
            padding: 1rem;
            margin: 1rem 0;
        }

        .validation-rules h5 {
            color: #c05621;
            margin-bottom: 0.5rem;
        }

        .validation-rules ul {
            margin-left: 1rem;
        }

        .validation-rules li {
            color: #744210;
            margin-bottom: 0.25rem;
        }

        .related-docs {
            background: #e6fffa;
            border-left: 4px solid #38b2ac;
            padding: 1rem;
            margin: 2rem 0;
        }

        .related-docs h4 {
            color: #234e52;
            margin-bottom: 0.5rem;
        }

        .related-docs ul {
            margin-left: 1rem;
        }

        .related-docs a {
            color: #2c7a7b;
            text-decoration: none;
        }

        .related-docs a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .documentation {
                padding: 1rem;
            }

            .doc-header h1 {
                font-size: 2rem;
            }

            .endpoint-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            nav ul {
                flex-direction: column;
                gap: 0.5rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="docs/index.html">Documentation</a></li>
                        <li><a href="docs/api.html" class="active">API</a></li>
                        <li><a href="docs/architecture.html">Architecture</a></li>
                        <li><a href="docs/database.html">Database</a></li>
                        <li><a href="docs/authentication.html">Auth</a></li>
                        <li><a href="docs/testing.html">Testing</a></li>
                        <li><a href="docs/setup.html">Setup</a></li>
                        <li><a href="docs/examples.html">Examples</a></li>
                        <li><a href="docs/validation.html">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="breadcrumb">
                    <a href="docs/index.html">← Back to Main Documentation</a> |
                    <a href="docs/architecture.html">Architecture →</a> |
                    <a href="docs/authentication.html">Authentication →</a>
                </div>

                <div class="doc-header">
                    <h1>API Documentation</h1>
                    <p class="doc-subtitle">Complete API reference for the 365 Manage Hours application</p>
                </div>

                <div class="related-docs">
                    <h4>Related Documentation:</h4>
                    <ul>
                        <li><a href="docs/authentication.html">Authentication & Authorization</a> - Detailed authentication flow</li>
                        <li><a href="docs/database.html">Database Schema</a> - Data structure and relationships</li>
                        <li><a href="docs/examples.html">Code Examples</a> - Implementation examples</li>
                    </ul>
                </div>

                <section class="doc-section">
                    <h2>Overview</h2>
                    <p>The 365 Manage Hours application provides a RESTful API for managing timesheets, user authentication, and CSV data processing. All endpoints follow standard HTTP conventions and return appropriate status codes.</p>
                </section>

                <section class="doc-section">
                    <h2>Authentication</h2>
                    <p>Most endpoints require user authentication via session middleware. Authentication is handled through session cookies.</p>

                    <h3>Authentication Endpoints</h3>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/login</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Authenticate user with email and password<br>
                                <strong>Middleware:</strong> GuestMiddleware (redirects authenticated users)
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Request Body (JSON):</h5>
                                    <pre><code>{
  "email": "<EMAIL>",
  "password": "password123"
}</code></pre>
                                </div>
                                <div class="validation-rules">
                                    <h5>Validation Rules:</h5>
                                    <ul>
                                        <li><code>email</code>: Required, valid email format</li>
                                        <li><code>password</code>: Required</li>
                                    </ul>
                                </div>
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Redirect to <code>/dashboard</code> on success, returns login form with errors on failure</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method">GET</span>
                            <span class="endpoint-path">/login</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Display login form<br>
                                <strong>Middleware:</strong> GuestMiddleware
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Login form view</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/register</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Register new user account<br>
                                <strong>Middleware:</strong> GuestMiddleware
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Request Body (JSON):</h5>
                                    <pre><code>{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "password_confirmation": "password123"
}</code></pre>
                                </div>
                                <div class="validation-rules">
                                    <h5>Validation Rules:</h5>
                                    <ul>
                                        <li><code>name</code>: Required</li>
                                        <li><code>email</code>: Required, valid email format, unique</li>
                                        <li><code>password</code>: Required, minimum 8 characters</li>
                                        <li><code>password_confirmation</code>: Required, must match password</li>
                                    </ul>
                                </div>
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Redirect to <code>/dashboard</code> on success, returns registration form with errors on failure</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method">GET</span>
                            <span class="endpoint-path">/register</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Display registration form<br>
                                <strong>Middleware:</strong> GuestMiddleware
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Registration form view</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/logout</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Logout current user
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Redirect to home page with success message</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Dashboard</h2>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method">GET</span>
                            <span class="endpoint-path">/dashboard</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Display user dashboard with paginated timesheets<br>
                                <strong>Middleware:</strong> AuthMiddleware
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Query Parameters:</h5>
                                    <ul>
                                        <li><code>page</code> (optional): Page number for pagination (default: 1)</li>
                                    </ul>
                                </div>
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Dashboard view with user's timesheets</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Timesheet Management</h2>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method">GET</span>
                            <span class="endpoint-path">/timesheets/{id}/edit</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Display timesheet edit form<br>
                                <strong>Middleware:</strong> AuthMiddleware
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Parameters:</h5>
                                    <ul>
                                        <li><code>id</code>: Timesheet ID (integer)</li>
                                    </ul>
                                </div>
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Edit form view or redirect with error if timesheet not found</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/timesheets/{id}</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Update existing timesheet<br>
                                <strong>Middleware:</strong> AuthMiddleware
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Parameters:</h5>
                                    <ul>
                                        <li><code>id</code>: Timesheet ID (integer)</li>
                                    </ul>
                                </div>
                                <div class="detail-section">
                                    <h5>Request Body (JSON):</h5>
                                    <pre><code>{
  "booking_year": "2024",
  "week_number": "30",
  "date": "2024-07-20",
  "employee_id": "320",
  "hours_worked": "8.5",
  "hours_worked_type": "REGULAR"
}</code></pre>
                                </div>
                                <div class="validation-rules">
                                    <h5>Validation Rules:</h5>
                                    <ul>
                                        <li><code>booking_year</code>: Required, numeric, valid year (1900-2100)</li>
                                        <li><code>week_number</code>: Required, numeric, range 1-53</li>
                                        <li><code>date</code>: Required, valid date format (Y-m-d)</li>
                                        <li><code>employee_id</code>: Required, numeric, positive integer</li>
                                        <li><code>hours_worked</code>: Required, numeric, non-negative</li>
                                        <li><code>hours_worked_type</code>: Required, string</li>
                                    </ul>
                                </div>
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Redirect to dashboard on success, returns edit form with errors on failure</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>CSV Processing</h2>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method post">POST</span>
                            <span class="endpoint-path">/process-csv</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Upload and process CSV timesheet file<br>
                                <strong>Middleware:</strong> AuthMiddleware<br>
                                <strong>Content-Type:</strong> multipart/form-data
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Request Body:</h5>
                                    <ul>
                                        <li><code>csvFile</code>: CSV file upload</li>
                                    </ul>
                                </div>
                                <div class="validation-rules">
                                    <h5>File Validation:</h5>
                                    <ul>
                                        <li>File type: CSV (text/csv, text/plain, application/csv)</li>
                                        <li>File size: Maximum 5MB</li>
                                        <li>Delimiter: Semicolon (;) required</li>
                                        <li>Headers: Must include Boekjaar, Week, Datum, Persnr, Uren, Uurcode</li>
                                    </ul>
                                </div>
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>Redirect to dashboard with success/error message</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="endpoint">
                        <div class="endpoint-header">
                            <span class="method">GET</span>
                            <span class="endpoint-path">/export</span>
                        </div>
                        <div class="endpoint-content">
                            <div class="endpoint-description">
                                <strong>Description:</strong> Export user timesheets as CSV file<br>
                                <strong>Middleware:</strong> AuthMiddleware
                            </div>
                            <div class="endpoint-details">
                                <div class="detail-section">
                                    <h5>Response:</h5>
                                    <p>CSV file download with filename <code>timesheets_YYYY-MM-DD.csv</code></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Error Handling</h2>
                    <p>All endpoints return appropriate HTTP status codes:</p>
                    <ul>
                        <li><code>200 OK</code>: Successful GET requests</li>
                        <li><code>302 Found</code>: Successful POST requests (redirects)</li>
                        <li><code>401 Unauthorized</code>: Authentication required</li>
                        <li><code>403 Forbidden</code>: Access denied</li>
                        <li><code>404 Not Found</code>: Resource not found</li>
                        <li><code>422 Unprocessable Entity</code>: Validation errors</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>Request/Response Examples</h2>

                    <h3>Successful Login</h3>
                    <pre><code>POST /login HTTP/1.1
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>&password=password123</code></pre>

                    <p><strong>Response:</strong></p>
                    <pre><code>HTTP/1.1 302 Found
Location: /dashboard?success=Welcome%20back!</code></pre>

                    <h3>Timesheet Update with Validation Error</h3>
                    <pre><code>POST /timesheets/123 HTTP/1.1
Content-Type: application/x-www-form-urlencoded

booking_year=&week_number=30&date=2024-07-20&employee_id=320&hours_worked=8.5&hours_worked_type=REGULAR</code></pre>

                    <p><strong>Response:</strong></p>
                    <pre><code>HTTP/1.1 200 OK
Content-Type: text/html

<!-- Edit form with validation errors --></code></pre>

                    <h3>CSV Export</h3>
                    <pre><code>GET /export HTTP/1.1
Cookie: session_id=abc123...</code></pre>

                    <p><strong>Response:</strong></p>
                    <pre><code>HTTP/1.1 200 OK
Content-Type: text/csv; charset=utf-8
Content-Disposition: attachment; filename="timesheets_2024-01-15.csv"

Boekjaar;Week;Datum;Persnr;Uren;Uurcode
2024;30;2024-07-20;320;8.5;REGULAR</code></pre>
                </section>
            </div>
        </div>
    </main>
</body>
</html>
