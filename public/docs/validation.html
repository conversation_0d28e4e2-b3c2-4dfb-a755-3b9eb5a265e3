<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Validation Reference - 365 Manage Hours</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        header { background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 1rem 0; position: sticky; top: 0; z-index: 100; }
        .header-content { display: flex; justify-content: space-between; align-items: center; }
        .logo { font-size: 1.5rem; font-weight: bold; color: #2563eb; text-decoration: none; }
        nav ul { list-style: none; display: flex; gap: 1rem; }
        nav a { color: #4b5563; text-decoration: none; font-weight: 500; padding: 0.5rem 1rem; border-radius: 4px; transition: all 0.2s; }
        nav a:hover, nav a.active { color: #2563eb; background-color: #eff6ff; }
        main { padding: 2rem 0; }
        .documentation { max-width: 1200px; margin: 0 auto; padding: 2rem; line-height: 1.6; background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .breadcrumb { margin-bottom: 2rem; padding: 1rem; background: #f7fafc; border-radius: 8px; font-size: 0.9rem; }
        .breadcrumb a { color: #4299e1; text-decoration: none; font-weight: 500; }
        .breadcrumb a:hover { text-decoration: underline; }
        .doc-header { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 2px solid #e2e8f0; }
        .doc-header h1 { font-size: 2.5rem; color: #2d3748; margin-bottom: 0.5rem; }
        .doc-subtitle { font-size: 1.25rem; color: #718096; margin-bottom: 1rem; }
        .doc-section { margin-bottom: 3rem; padding-bottom: 2rem; border-bottom: 1px solid #e2e8f0; }
        .doc-section:last-child { border-bottom: none; }
        .doc-section h2 { color: #2d3748; font-size: 2rem; margin-bottom: 1rem; }
        .doc-section h3 { color: #4a5568; font-size: 1.5rem; margin-top: 2rem; margin-bottom: 1rem; }
        .doc-section h4 { color: #718096; font-size: 1.25rem; margin-top: 1.5rem; margin-bottom: 0.75rem; }
        .related-docs { background: #e6fffa; border-left: 4px solid #38b2ac; padding: 1rem; margin: 2rem 0; }
        .related-docs h4 { color: #234e52; margin-bottom: 0.5rem; }
        .related-docs ul { margin-left: 1rem; }
        .related-docs a { color: #2c7a7b; text-decoration: none; }
        .related-docs a:hover { text-decoration: underline; }
        .validation-card { background: #f7fafc; border: 1px solid #e2e8f0; border-radius: 8px; margin: 1.5rem 0; overflow: hidden; }
        .validation-header { background: #553c9a; color: #fff; padding: 1rem; font-weight: 600; }
        .validation-content { padding: 1.5rem; }
        .validation-rules { background: #fef5e7; border-left: 4px solid #ed8936; padding: 1rem; margin: 1rem 0; border-radius: 4px; }
        .validation-rules h5 { color: #c05621; margin-bottom: 0.5rem; }
        .validation-rules ul { margin-left: 1rem; }
        .validation-rules li { color: #744210; margin-bottom: 0.25rem; }
        .layer-card { background: #e6fffa; border-left: 4px solid #38b2ac; padding: 1.5rem; margin: 1.5rem 0; border-radius: 8px; }
        .layer-card h4 { color: #234e52; margin-top: 0; }
        .layer-card ul { margin-left: 1rem; }
        .layer-card li { color: #2c7a7b; margin-bottom: 0.5rem; }
        code { background: #f1f5f9; padding: 0.2rem 0.4rem; border-radius: 4px; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.9em; color: #e53e3e; }
        pre { background: #2d3748; color: #e2e8f0; padding: 1rem; border-radius: 8px; overflow-x: auto; margin: 1rem 0; }
        pre code { background: none; padding: 0; color: inherit; }
        .validation-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem; margin: 1.5rem 0; }
        @media (max-width: 768px) { .documentation { padding: 1rem; } .doc-header h1 { font-size: 2rem; } .validation-grid { grid-template-columns: 1fr; } nav ul { flex-direction: column; gap: 0.5rem; } .header-content { flex-direction: column; gap: 1rem; } }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <a href="/" class="logo">365 Manage Hours</a>
                <nav>
                    <ul>
                        <li><a href="index.html">Documentation</a></li>
                        <li><a href="api.html">API</a></li>
                        <li><a href="architecture.html">Architecture</a></li>
                        <li><a href="database.html">Database</a></li>
                        <li><a href="authentication.html">Auth</a></li>
                        <li><a href="testing.html">Testing</a></li>
                        <li><a href="setup.html">Setup</a></li>
                        <li><a href="examples.html">Examples</a></li>
                        <li><a href="validation.html" class="active">Validation</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <div class="container">
            <div class="documentation">
                <div class="breadcrumb">
                    <a href="index.html">← Back to Main Documentation</a> | 
                    <a href="api.html">API Documentation →</a> | 
                    <a href="examples.html">Code Examples →</a>
                </div>

                <div class="doc-header">
                    <h1>Validation Reference</h1>
                    <p class="doc-subtitle">Comprehensive reference for all validation rules, business logic, and data constraints</p>
                </div>

                <div class="related-docs">
                    <h4>Related Documentation:</h4>
                    <ul>
                        <li><a href="api.html">API Documentation</a> - Endpoint validation requirements</li>
                        <li><a href="examples.html">Code Examples</a> - Validation implementation examples</li>
                        <li><a href="testing.html">Testing Documentation</a> - Validation testing patterns</li>
                    </ul>
                </div>

                <section class="doc-section">
                    <h2>Overview</h2>
                    <p>This document provides a comprehensive reference for all validation rules, business logic, and data constraints used throughout the 365 Manage Hours application.</p>
                </section>

                <section class="doc-section">
                    <h2>Request Validation Classes</h2>
                    
                    <div class="validation-grid">
                        <div class="validation-card">
                            <div class="validation-header">LoginRequest</div>
                            <div class="validation-content">
                                <p><strong>Purpose:</strong> Validates user login credentials</p>
                                
                                <h5>Fields:</h5>
                                <ul>
                                    <li><code>email</code>: Required, valid email format</li>
                                    <li><code>password</code>: Required</li>
                                </ul>

                                <pre><code>final class LoginRequest implements Request
{
    public function __construct(
        public string $email,
        public string $password,
    ) {}
}</code></pre>

                                <div class="validation-rules">
                                    <h5>Validation Rules:</h5>
                                    <ul>
                                        <li>Email format validation using <code>filter_var()</code></li>
                                        <li>Required field validation</li>
                                        <li>No additional business rules</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="validation-card">
                            <div class="validation-header">RegisterRequest</div>
                            <div class="validation-content">
                                <p><strong>Purpose:</strong> Validates user registration data</p>
                                
                                <h5>Fields:</h5>
                                <ul>
                                    <li><code>name</code>: Required</li>
                                    <li><code>email</code>: Required, valid email format, unique</li>
                                    <li><code>password</code>: Required, minimum 8 characters</li>
                                    <li><code>password_confirmation</code>: Required, must match password</li>
                                </ul>

                                <pre><code>#[Email]
public string $email;

#[Length(min: 8)]
public string $password;</code></pre>

                                <div class="validation-rules">
                                    <h5>Custom Validation:</h5>
                                    <ul>
                                        <li>Password confirmation matching</li>
                                        <li>Email uniqueness check (repository level)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="validation-card">
                            <div class="validation-header">TimesheetRequest</div>
                            <div class="validation-content">
                                <p><strong>Purpose:</strong> Validates timesheet data entry and updates</p>
                                
                                <h5>Fields:</h5>
                                <ul>
                                    <li><code>booking_year</code>: Required, numeric, range 1900-2100</li>
                                    <li><code>week_number</code>: Required, numeric, range 1-53</li>
                                    <li><code>date</code>: Required, valid date format (Y-m-d)</li>
                                    <li><code>employee_id</code>: Required, numeric, positive integer</li>
                                    <li><code>hours_worked</code>: Required, numeric, non-negative</li>
                                    <li><code>hours_worked_type</code>: Required, string</li>
                                </ul>

                                <pre><code>#[Numeric]
public string $booking_year;

#[Numeric]
public string $week_number;

#[Numeric]
public string $employee_id;

#[Numeric]
public string $hours_worked;</code></pre>

                                <div class="validation-rules">
                                    <h5>Business Rules:</h5>
                                    <ul>
                                        <li>Year validation: 1900-2100</li>
                                        <li>Week validation: 1-53</li>
                                        <li>Date format validation</li>
                                        <li>Hours worked: non-negative</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="validation-card">
                            <div class="validation-header">CsvUploadRequest</div>
                            <div class="validation-content">
                                <p><strong>Purpose:</strong> Validates CSV file uploads</p>
                                
                                <h5>Fields:</h5>
                                <ul>
                                    <li><code>files</code>: Array containing uploaded file data</li>
                                </ul>

                                <div class="validation-rules">
                                    <h5>File Validation Rules:</h5>
                                    <ul>
                                        <li><strong>File presence:</strong> Must include 'csvFile' key</li>
                                        <li><strong>Upload errors:</strong> Must have UPLOAD_ERR_OK status</li>
                                        <li><strong>File type:</strong> Must be text/csv, text/plain, or application/csv</li>
                                        <li><strong>File size:</strong> Maximum 5MB</li>
                                        <li><strong>Content validation:</strong> Must contain semicolon delimiter</li>
                                        <li><strong>Non-empty:</strong> File cannot be empty</li>
                                    </ul>
                                </div>

                                <pre><code>public function validateCsvFile(): array
{
    $errors = [];

    if (!isset($this->files['csvFile'])) {
        $errors['csvFile'] = 'Please select a CSV file';
        return $errors;
    }

    $file = $this->files['csvFile'];

    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors['csvFile'] = 'Please select a valid CSV file';
        return $errors;
    }

    // File type validation
    $allowedTypes = ['text/csv', 'text/plain', 'application/csv'];
    if (!in_array($file['type'], $allowedTypes)) {
        $errors['csvFile'] = 'Please upload a CSV file';
    }

    // File size validation (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        $errors['csvFile'] = 'File size must be less than 5MB';
    }

    return $errors;
}</code></pre>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Database Constraints</h2>
                    
                    <h3>User Model Constraints</h3>
                    <p><strong>Table:</strong> <code>users</code></p>
                    
                    <div class="validation-rules">
                        <h5>Constraints:</h5>
                        <ul>
                            <li><code>id</code>: Primary key, auto-increment</li>
                            <li><code>name</code>: NOT NULL, max 255 characters</li>
                            <li><code>email</code>: NOT NULL, UNIQUE, max 255 characters, valid email format</li>
                            <li><code>password</code>: NOT NULL, hashed with <code>password_hash()</code></li>
                            <li><code>created_at</code>: NOT NULL, datetime</li>
                            <li><code>updated_at</code>: NOT NULL, datetime</li>
                        </ul>
                    </div>

                    <pre><code>#[Length(min: 1, max: 255)]
public string $name;

#[Email]
#[Length(max: 255)]
public string $email;</code></pre>

                    <h3>Timesheet Model Constraints</h3>
                    <p><strong>Table:</strong> <code>timesheets</code></p>
                    
                    <div class="validation-rules">
                        <h5>Constraints:</h5>
                        <ul>
                            <li><code>id</code>: Primary key, auto-increment</li>
                            <li><code>user_id</code>: NOT NULL, foreign key to users.id</li>
                            <li><code>booking_year</code>: NOT NULL, integer</li>
                            <li><code>week_number</code>: NOT NULL, integer</li>
                            <li><code>date</code>: NOT NULL, date</li>
                            <li><code>employee_id</code>: NOT NULL, integer</li>
                            <li><code>hours_worked</code>: NOT NULL, real/float</li>
                            <li><code>hours_worked_type</code>: NOT NULL, text</li>
                            <li><code>created_at</code>: NOT NULL, datetime</li>
                            <li><code>updated_at</code>: NOT NULL, datetime</li>
                        </ul>
                    </div>

                    <p><strong>Foreign Key Constraints:</strong></p>
                    <ul>
                        <li><code>user_id</code> references <code>users.id</code> with CASCADE DELETE</li>
                    </ul>
                </section>

                <section class="doc-section">
                    <h2>CSV Data Validation</h2>
                    
                    <h3>CSV Structure Requirements</h3>
                    <ul>
                        <li><strong>Delimiter:</strong> Semicolon (;)</li>
                        <li><strong>Headers:</strong> Must include all required columns</li>
                        <li><strong>Encoding:</strong> UTF-8 recommended</li>
                    </ul>

                    <div class="validation-rules">
                        <h5>Required Headers (Dutch format):</h5>
                        <ul>
                            <li><code>Boekjaar</code>: Booking year (integer)</li>
                            <li><code>Week</code>: Week number (integer)</li>
                            <li><code>Datum</code>: Date (YYYY-MM-DD format)</li>
                            <li><code>Persnr</code>: Employee number (integer)</li>
                            <li><code>Uren</code>: Hours worked (numeric)</li>
                            <li><code>Uurcode</code>: Hour type code (string)</li>
                        </ul>
                    </div>

                    <pre><code>final class Csv
{
    public function validate(): bool
    {
        return !empty($this->Boekjaar) &&
               !empty($this->Week) &&
               !empty($this->Datum) &&
               !empty($this->Persnr) &&
               !empty($this->Uren) &&
               !empty($this->Uurcode);
    }

    public function toModelAttributes(): array
    {
        return [
            'booking_year' => $this->Boekjaar,
            'week_number' => $this->Week,
            'date' => $this->Datum,
            'employee_id' => $this->Persnr,
            'hours_worked' => (float) $this->Uren,
            'hours_worked_type' => $this->Uurcode,
        ];
    }
}</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Validation Layers</h2>
                    
                    <div class="validation-grid">
                        <div class="layer-card">
                            <h4>1. Client-Side Validation (HTML5)</h4>
                            <p>Basic HTML5 validation attributes:</p>
                            <ul>
                                <li><code>required</code> for mandatory fields</li>
                                <li><code>type="email"</code> for email validation</li>
                                <li><code>accept=".csv,.txt"</code> for file uploads</li>
                                <li><code>min</code>/<code>max</code> for numeric ranges</li>
                            </ul>
                        </div>

                        <div class="layer-card">
                            <h4>2. Request Class Validation (Tempest Attributes)</h4>
                            <p>PHP attributes for type validation:</p>
                            <ul>
                                <li><code>#[Email]</code>: Email format validation</li>
                                <li><code>#[Numeric]</code>: Numeric value validation</li>
                                <li><code>#[Length(min: X, max: Y)]</code>: String length validation</li>
                            </ul>
                        </div>

                        <div class="layer-card">
                            <h4>3. Business Rules Validation (Custom Methods)</h4>
                            <p>Custom validation methods in Request classes:</p>
                            <ul>
                                <li>Date format validation</li>
                                <li>Range validation (years, weeks)</li>
                                <li>Cross-field validation (password confirmation)</li>
                                <li>File content validation</li>
                            </ul>
                        </div>

                        <div class="layer-card">
                            <h4>4. Database Constraints</h4>
                            <p>Database-level constraints:</p>
                            <ul>
                                <li>Primary key constraints</li>
                                <li>Foreign key constraints</li>
                                <li>Unique constraints</li>
                                <li>NOT NULL constraints</li>
                            </ul>
                        </div>

                        <div class="layer-card">
                            <h4>5. Repository-Level Validation</h4>
                            <p>Additional checks in repository methods:</p>
                            <ul>
                                <li>User ownership verification</li>
                                <li>Data existence checks</li>
                                <li>Business logic enforcement</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="doc-section">
                    <h2>Error Handling Patterns</h2>
                    
                    <h3>Validation Error Response</h3>
                    <pre><code>// Controller validation pattern
$errors = $this->validateTimesheetRequest($timesheetRequest);
if (!empty($errors)) {
    return $this->viewService->withErrors('timesheet/edit', $errors, [
        'timesheet' => $timesheet,
        'old' => $request->body
    ]);
}</code></pre>

                    <h3>Error Message Format</h3>
                    <p><strong>Field-specific errors:</strong></p>
                    <pre><code>$errors = [
    'email' => 'Email is required',
    'password' => 'Password must be at least 8 characters',
    'week_number' => 'Week number must be between 1 and 53'
];</code></pre>

                    <p><strong>General error messages:</strong></p>
                    <pre><code>return $this->viewService->withError('dashboard', 'An error occurred', [
    'timesheets' => []
]);</code></pre>
                </section>

                <section class="doc-section">
                    <h2>Testing Validation</h2>
                    
                    <h3>Unit Tests</h3>
                    <pre><code>public function test_validates_business_rules(): void
{
    $request = new TimesheetRequest(/* invalid data */);
    $errors = $request->validateBusinessRules();
    $this->assertArrayHasKey('week_number', $errors);
}</code></pre>

                    <h3>Feature Tests</h3>
                    <pre><code>public function test_rejects_invalid_timesheet_data(): void
{
    $response = $this->http->post("/timesheets/{$id}", $invalidData);
    $response->assertOk(); // Returns form with errors
    $this->assertTimesheetUnchanged($id);
}</code></pre>

                    <h3>Data Providers</h3>
                    <pre><code>#[DataProvider('invalidDateProvider')]
public function test_rejects_invalid_dates(string $invalidDate): void
{
    // Test implementation
}

public static function invalidDateProvider(): array
{
    return [
        'invalid format' => ['invalid-date'],
        'impossible date' => ['2024-13-45'],
        'letters in date' => ['2024-ab-cd'],
    ];
}</code></pre>
                </section>
            </div>
        </div>
    </main>
</body>
</html>
